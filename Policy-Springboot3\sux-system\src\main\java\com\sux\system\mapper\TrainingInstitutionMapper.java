package com.sux.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sux.system.domain.TrainingInstitution;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 培训机构信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface TrainingInstitutionMapper extends BaseMapper<TrainingInstitution>
{
    /**
     * 查询培训机构信息
     * 
     * @param institutionId 培训机构信息主键
     * @return 培训机构信息
     */
    public TrainingInstitution selectTrainingInstitutionByInstitutionId(Long institutionId);

    /**
     * 查询培训机构信息列表
     * 
     * @param trainingInstitution 培训机构信息
     * @return 培训机构信息集合
     */
    public List<TrainingInstitution> selectTrainingInstitutionList(TrainingInstitution trainingInstitution);

    /**
     * 新增培训机构信息
     * 
     * @param trainingInstitution 培训机构信息
     * @return 结果
     */
    public int insertTrainingInstitution(TrainingInstitution trainingInstitution);

    /**
     * 修改培训机构信息
     * 
     * @param trainingInstitution 培训机构信息
     * @return 结果
     */
    public int updateTrainingInstitution(TrainingInstitution trainingInstitution);

    /**
     * 删除培训机构信息
     * 
     * @param institutionId 培训机构信息主键
     * @return 结果
     */
    public int deleteTrainingInstitutionByInstitutionId(Long institutionId);

    /**
     * 批量删除培训机构信息
     * 
     * @param institutionIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTrainingInstitutionByInstitutionIds(Long[] institutionIds);

    /**
     * 检查机构编码是否唯一
     * 
     * @param institutionCode 机构编码
     * @param institutionId 机构ID
     * @return 结果
     */
    public TrainingInstitution checkInstitutionCodeUnique(@Param("institutionCode") String institutionCode, @Param("institutionId") Long institutionId);

    /**
     * 检查机构名称是否唯一
     * 
     * @param institutionName 机构名称
     * @param institutionId 机构ID
     * @return 结果
     */
    public TrainingInstitution checkInstitutionNameUnique(@Param("institutionName") String institutionName, @Param("institutionId") Long institutionId);

    /**
     * 根据状态查询机构列表
     * 
     * @param status 状态
     * @return 机构列表
     */
    public List<TrainingInstitution> selectTrainingInstitutionByStatus(@Param("status") String status);

    /**
     * 根据专业领域查询机构列表
     * 
     * @param specialty 专业领域
     * @return 机构列表
     */
    public List<TrainingInstitution> selectTrainingInstitutionBySpecialty(@Param("specialty") String specialty);

    /**
     * 统计各状态机构数量
     * 
     * @return 统计结果
     */
    public List<TrainingInstitution> selectInstitutionStatusStatistics();
}
