package com.sux.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sux.common.annotation.Excel;
import com.sux.common.core.domain.BaseEntity;
import jakarta.validation.constraints.*;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 零工信息对象 worker_profile（核心匹配优化版）
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
@TableName("worker_profile")
public class WorkerProfile extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 零工ID */
    @TableId(type = IdType.AUTO)
    private Long workerId;

    /** 用户ID */
    @Excel(name = "用户ID")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /** 真实姓名 */
    @Excel(name = "真实姓名")
    @NotBlank(message = "真实姓名不能为空")
    @Size(min = 2, max = 100, message = "真实姓名长度必须介于 2 和 100 之间")
    private String realName;

    /** 昵称 */
    @Excel(name = "昵称")
    @Size(min = 0, max = 100, message = "昵称不能超过100个字符")
    private String nickname;

    /** 性别（male/female） */
    @Excel(name = "性别")
    private String gender;

    /** 年龄 */
    @Excel(name = "年龄")
    @Min(value = 16, message = "年龄不能小于16岁")
    @Max(value = 80, message = "年龄不能大于80岁")
    private Integer age;

    /** 手机号 */
    @Excel(name = "手机号")
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    /** 当前所在地（保留但不作为主要匹配条件） */
    @Excel(name = "当前所在地")
    @Size(min = 0, max = 200, message = "当前所在地不能超过200个字符")
    private String currentLocation;

    /** 学历水平（不限/初中/高中/中专/大专/本科/硕士/博士） - 核心匹配字段 */
    @Excel(name = "学历水平")
    @Size(min = 0, max = 50, message = "学历水平不能超过50个字符")
    private String educationLevel;

    /** 工作经验年数 */
    @Excel(name = "工作经验年数")
    @Min(value = 0, message = "工作经验年数不能小于0")
    private Integer workExperienceYears;

    /** 工作类别偏好（JSON数组：服务员/保洁/搬运工/销售/厨师助手/快递员/保安等） - 核心匹配字段 */
    @Excel(name = "工作类别偏好")
    private String workCategories;

    /** 偏好工作类型（JSON数组：全职/兼职/临时工/小时工） - 核心匹配字段 */
    @Excel(name = "偏好工作类型")
    private String jobTypesPreferred;

    /** 技能列表（JSON格式） */
    @Excel(name = "技能列表")
    private String skills;

    /** 期望最低薪资 */
    @Excel(name = "期望最低薪资")
    @DecimalMin(value = "0.0", message = "期望最低薪资不能小于0")
    private BigDecimal salaryExpectationMin;

    /** 期望最高薪资 */
    @Excel(name = "期望最高薪资")
    @DecimalMin(value = "0.0", message = "期望最高薪资不能小于0")
    private BigDecimal salaryExpectationMax;

    /** 薪资类型偏好（hourly/daily/monthly/piece） - 核心匹配字段 */
    @Excel(name = "薪资类型偏好")
    private String salaryTypePreference;

    /** 可开始工作日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "可开始工作日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date availabilityStartDate;

    /** 每周可工作天数 */
    @Excel(name = "每周可工作天数")
    @Min(value = 1, message = "每周可工作天数不能小于1")
    @Max(value = 7, message = "每周可工作天数不能大于7")
    private Integer workDaysPerWeek;

    /** 每日可工作小时数 */
    @Excel(name = "每日可工作小时数")
    @Min(value = 1, message = "每日可工作小时数不能小于1")
    @Max(value = 24, message = "每日可工作小时数不能大于24")
    private Integer workHoursPerDay;

    /** 头像照片URL */
    @Excel(name = "头像照片URL")
    @Size(min = 0, max = 500, message = "头像照片URL不能超过500个字符")
    private String profilePhoto;

    /** 自我介绍 */
    @Excel(name = "自我介绍")
    private String selfIntroduction;

    /** 平均评分 */
    @Excel(name = "平均评分")
    @DecimalMin(value = "0.0", message = "平均评分不能小于0")
    @DecimalMax(value = "5.0", message = "平均评分不能大于5")
    private BigDecimal ratingAverage;

    /** 评分次数 */
    @Excel(name = "评分次数")
    @Min(value = 0, message = "评分次数不能小于0")
    private Integer ratingCount;

    /** 完成工作数量 */
    @Excel(name = "完成工作数量")
    @Min(value = 0, message = "完成工作数量不能小于0")
    private Integer completedJobs;

    /** 成功率 */
    @Excel(name = "成功率")
    @DecimalMin(value = "0.0", message = "成功率不能小于0")
    @DecimalMax(value = "100.0", message = "成功率不能大于100")
    private BigDecimal successRate;

    /** 状态（active/inactive/suspended/banned） */
    @Excel(name = "状态", readConverterExp = "active=活跃,inactive=不活跃,suspended=暂停,banned=禁用")
    private String status;

    /** 是否已实名验证（0否 1是） */
    @Excel(name = "是否已实名验证", readConverterExp = "0=否,1=是")
    private Integer isVerified;

    /** 最后活跃时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后活跃时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastActiveTime;

    /** 验证时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "验证时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date verificationTime;

    /** 删除标志（0代表存在 2代表删除） */
    @TableField("del_flag")
    private String delFlag;

    // 关联查询字段
    /** 用户名 */
    @TableField(exist = false)
    private String userName;

    /** 用户昵称 */
    @TableField(exist = false)
    private String userNickName;

}
