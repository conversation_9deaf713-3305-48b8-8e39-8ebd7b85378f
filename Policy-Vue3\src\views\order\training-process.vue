<template>
  <div class="training-process-container app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">培训订单流程管理</h1>
      <p class="page-subtitle">企业发布培训订单，线上招募培训机构的完整流程管理</p>
    </div>

    <!-- 流程步骤展示 -->
    <div class="process-steps-section">
      <h2 class="section-title">培训订单发布流程</h2>
      <el-steps :active="currentStep" finish-status="success" align-center>
        <el-step title="创建订单" description="企业创建培训需求订单" icon="Edit"></el-step>
        <el-step title="发布招募" description="发布订单，招募培训机构" icon="Promotion"></el-step>
        <el-step title="机构申请" description="培训机构提交申请方案" icon="Document"></el-step>
        <el-step title="方案评审" description="企业评审培训方案" icon="View"></el-step>
        <el-step title="确定合作" description="选定培训机构开始合作" icon="Check"></el-step>
      </el-steps>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon total">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.totalOrders }}</div>
              <div class="stat-label">总订单数</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon published">
              <el-icon><Promotion /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.publishedOrders }}</div>
              <div class="stat-label">已发布</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon applications">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.totalApplications }}</div>
              <div class="stat-label">申请总数</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon completed">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.completedOrders }}</div>
              <div class="stat-label">已完成</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 培训订单管理 -->
    <div class="order-management-section">
      <div class="section-header">
        <h2 class="section-title">培训订单管理</h2>
        <div class="header-actions">
          <el-button type="primary" @click="handleCreateOrder">
            <el-icon><Plus /></el-icon>
            发布新订单
          </el-button>
          <el-button type="success" @click="handleBatchPublish" :disabled="selectedOrders.length === 0">
            <el-icon><Promotion /></el-icon>
            批量发布
          </el-button>
        </div>
      </div>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-form :model="queryParams" inline>
          <el-form-item label="订单状态">
            <el-select v-model="queryParams.orderStatus" placeholder="请选择状态" clearable @change="handleSearch">
              <el-option label="全部" value=""></el-option>
              <el-option label="草稿" value="0"></el-option>
              <el-option label="已发布" value="1"></el-option>
              <el-option label="进行中" value="2"></el-option>
              <el-option label="已完成" value="3"></el-option>
              <el-option label="已取消" value="4"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="培训类型">
            <el-select v-model="queryParams.trainingType" placeholder="请选择类型" clearable @change="handleSearch">
              <el-option label="全部" value=""></el-option>
              <el-option label="技术培训" value="技术培训"></el-option>
              <el-option label="管理培训" value="管理培训"></el-option>
              <el-option label="职业技能" value="职业技能"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="关键词">
            <el-input
              v-model="queryParams.keyword"
              placeholder="请输入订单标题"
              style="width: 200px;"
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 订单列表 -->
      <div class="order-list">
        <el-table
          :data="orderList"
          stripe
          @selection-change="handleSelectionChange"
          v-loading="loading"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="orderTitle" label="订单标题" min-width="200">
            <template #default="{ row }">
              <el-link type="primary" @click="viewOrderDetail(row)">
                {{ row.orderTitle }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column prop="trainingType" label="培训类型" width="120" />
          <el-table-column prop="trainingLevel" label="培训级别" width="100" />
          <el-table-column prop="maxParticipants" label="招生人数" width="100" />
          <el-table-column prop="trainingFee" label="培训费用" width="120">
            <template #default="{ row }">
              <span v-if="row.trainingFee > 0">¥{{ row.trainingFee }}</span>
              <el-tag v-else type="success" size="small">免费</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="orderStatus" label="订单状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getOrderStatusType(row.orderStatus)">
                {{ getOrderStatusText(row.orderStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="申请情况" width="120">
            <template #default="{ row }">
              <el-badge :value="row.applicationCount || 0" class="item">
                <el-button size="small" @click="viewApplications(row)">
                  查看申请
                </el-button>
              </el-badge>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="250" fixed="right">
            <template #default="{ row }">
              <el-button size="small" @click="viewOrderDetail(row)">详情</el-button>
              <el-button size="small" type="primary" @click="editOrder(row)">编辑</el-button>
              <el-button
                v-if="row.orderStatus === '0'"
                size="small"
                type="success"
                @click="publishOrder(row)"
              >
                发布
              </el-button>
              <el-button
                v-if="row.orderStatus === '1'"
                size="small"
                type="warning"
                @click="viewApplications(row)"
              >
                申请管理
              </el-button>
              <el-dropdown @command="(command) => handleOrderAction(command, row)">
                <el-button size="small">
                  更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="cancel" v-if="['1','2'].includes(row.orderStatus)">取消订单</el-dropdown-item>
                    <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="queryParams.pageNum"
            v-model:page-size="queryParams.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 订单详情弹窗 -->
    <OrderDetailDialog ref="orderDetailDialogRef" />

    <!-- 申请管理弹窗 -->
    <ApplicationManagementDialog
      ref="applicationManagementDialogRef"
      @approve="handleApplicationApprove"
      @reject="handleApplicationReject"
    />
  </div>
</template>

<script setup name="TrainingProcess">
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import {
  Document,
  Promotion,
  User,
  Check,
  Plus,
  Search,
  Edit,
  View,
  ArrowDown
} from '@element-plus/icons-vue'
import { listTrainingOrder, publishTrainingOrder, cancelTrainingOrder, delTrainingOrder } from "@/api/training/order"
import OrderDetailDialog from './components/OrderDetailDialog.vue'
import ApplicationManagementDialog from './components/ApplicationManagementDialog.vue'

const { proxy } = getCurrentInstance()

// 响应式数据
const currentStep = ref(2) // 当前流程步骤
const loading = ref(false)
const selectedOrders = ref([])

// 统计数据
const stats = reactive({
  totalOrders: 0,
  publishedOrders: 0,
  totalApplications: 0,
  completedOrders: 0
})

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  orderStatus: '',
  trainingType: '',
  keyword: ''
})

// 订单列表数据
const orderList = ref([])
const total = ref(0)

// 弹窗引用
const orderDetailDialogRef = ref(null)
const applicationManagementDialogRef = ref(null)

// 初始化
onMounted(() => {
  loadOrderList()
  loadStats()
})

// 加载订单列表
const loadOrderList = async () => {
  loading.value = true
  try {
    const response = await listTrainingOrder(queryParams)
    orderList.value = response.rows || []
    total.value = response.total || 0
  } catch (error) {
    console.error('加载订单列表失败:', error)
    proxy.$modal.msgError('加载订单列表失败')
  } finally {
    loading.value = false
  }
}

// 加载统计数据
const loadStats = async () => {
  try {
    // 这里应该调用统计API
    // const response = await getTrainingOrderStats()
    // Object.assign(stats, response.data)

    // 临时模拟数据
    stats.totalOrders = 25
    stats.publishedOrders = 18
    stats.totalApplications = 42
    stats.completedOrders = 8
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 搜索处理
const handleSearch = () => {
  queryParams.pageNum = 1
  loadOrderList()
}

// 重置搜索
const resetSearch = () => {
  queryParams.orderStatus = ''
  queryParams.trainingType = ''
  queryParams.keyword = ''
  queryParams.pageNum = 1
  loadOrderList()
}

// 选择变化处理
const handleSelectionChange = (selection) => {
  selectedOrders.value = selection
}

// 分页处理
const handleSizeChange = (size) => {
  queryParams.pageSize = size
  queryParams.pageNum = 1
  loadOrderList()
}

const handleCurrentChange = (page) => {
  queryParams.pageNum = page
  loadOrderList()
}

// 创建新订单
const handleCreateOrder = () => {
  proxy.$router.push('/order/index')
}

// 批量发布
const handleBatchPublish = async () => {
  if (selectedOrders.value.length === 0) {
    proxy.$modal.msgWarning('请选择要发布的订单')
    return
  }

  try {
    await proxy.$modal.confirm(`确认要批量发布选中的 ${selectedOrders.value.length} 个订单吗？`)

    const promises = selectedOrders.value.map(order => publishTrainingOrder(order.orderId))
    await Promise.all(promises)

    proxy.$modal.msgSuccess('批量发布成功')
    loadOrderList()
    loadStats()
  } catch (error) {
    console.error('批量发布失败:', error)
    proxy.$modal.msgError('批量发布失败')
  }
}

// 查看订单详情
const viewOrderDetail = (order) => {
  orderDetailDialogRef.value?.openDialog(order)
}

// 编辑订单
const editOrder = (order) => {
  proxy.$router.push(`/order/edit/${order.orderId}`)
}

// 发布订单
const publishOrder = async (order) => {
  try {
    await proxy.$modal.confirm(`确认要发布"${order.orderTitle}"培训订单吗？`)
    await publishTrainingOrder(order.orderId)
    proxy.$modal.msgSuccess('发布成功')
    loadOrderList()
    loadStats()
  } catch (error) {
    console.error('发布失败:', error)
    proxy.$modal.msgError('发布失败')
  }
}