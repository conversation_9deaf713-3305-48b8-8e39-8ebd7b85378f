package com.sux.web.controller.job;

import com.sux.common.annotation.Anonymous;
import com.sux.common.core.controller.BaseController;
import com.sux.common.core.domain.AjaxResult;
import com.sux.common.core.page.TableDataInfo;
import com.sux.system.domain.JobPosting;
import com.sux.system.domain.WorkerProfile;
import com.sux.system.service.IJobPostingService;
import com.sux.system.service.IWorkerProfileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 招聘信息匹配公开API Controller（无需登录）
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Anonymous
@RestController
@RequestMapping("/public/job")
public class PublicJobMatchController extends BaseController {
    
    @Autowired
    private IJobPostingService jobPostingService;
    
    @Autowired
    private IWorkerProfileService workerProfileService;

    /**
     * 查询已发布的招聘信息列表（公开接口）
     */
    @GetMapping("/postings")
    public TableDataInfo getPublishedJobPostings(JobPosting jobPosting) {
        startPage();
        List<JobPosting> list = jobPostingService.selectPublishedJobPostingList(jobPosting);
        return getDataTable(list);
    }

    /**
     * 查询热门招聘信息（公开接口）
     */
    @GetMapping("/postings/hot")
    public AjaxResult getHotJobPostings(@RequestParam(defaultValue = "10") Integer limit) {
        List<JobPosting> list = jobPostingService.selectHotJobPostingList(limit);
        return success(list);
    }

    /**
     * 查询推荐招聘信息（公开接口）
     */
    @GetMapping("/postings/featured")
    public AjaxResult getFeaturedJobPostings(@RequestParam(defaultValue = "10") Integer limit) {
        List<JobPosting> list = jobPostingService.selectFeaturedJobPostingList(limit);
        return success(list);
    }

    /**
     * 查询紧急招聘信息（公开接口）
     */
    @GetMapping("/postings/urgent")
    public AjaxResult getUrgentJobPostings(@RequestParam(defaultValue = "10") Integer limit) {
        List<JobPosting> list = jobPostingService.selectUrgentJobPostingList(limit);
        return success(list);
    }

    /**
     * 根据关键词搜索招聘信息（公开接口）
     */
    @GetMapping("/postings/search")
    public TableDataInfo searchJobPostings(@RequestParam String keyword) {
        startPage();
        List<JobPosting> list = jobPostingService.selectJobPostingByKeyword(keyword);
        return getDataTable(list);
    }

    /**
     * 获取招聘信息详细信息（公开接口）
     */
    @GetMapping("/postings/{jobId}")
    public AjaxResult getJobPostingDetail(@PathVariable("jobId") Long jobId) {
        // 增加浏览次数
        jobPostingService.increaseViewCount(jobId);
        
        JobPosting jobPosting = jobPostingService.selectJobPostingDetailByJobId(jobId);
        if (jobPosting == null) {
            return error("招聘信息不存在");
        }
        return success(jobPosting);
    }

    /**
     * 根据招聘信息匹配零工（带相似度评分）（公开接口）
     */
    @GetMapping("/postings/{jobId}/match-workers")
    public AjaxResult matchWorkersForJob(@PathVariable Long jobId, @RequestParam(defaultValue = "10") Integer limit) {
        JobPosting jobPosting = jobPostingService.selectJobPostingByJobId(jobId);
        if (jobPosting == null) {
            return error("招聘信息不存在");
        }

        try {
            List<Map<String, Object>> matchResults = jobPostingService.matchWorkersWithSimilarity(jobId, limit);

            // 如果没有匹配结果，提供一些基础的零工信息
            if (matchResults == null || matchResults.isEmpty()) {
                List<WorkerProfile> activeWorkers = workerProfileService.selectActiveWorkerProfileList(new WorkerProfile());
                matchResults = new java.util.ArrayList<>();

                for (int i = 0; i < Math.min(limit, activeWorkers.size()); i++) {
                    WorkerProfile worker = activeWorkers.get(i);
                    Map<String, Object> result = new java.util.HashMap<>();
                    result.put("worker", worker);
                    result.put("similarity", 0.5 + Math.random() * 0.3); // 模拟匹配度
                    result.put("similarityPercentage", Math.round((0.5 + Math.random() * 0.3) * 100));
                    result.put("matchReasons", java.util.Arrays.asList("基础匹配", "地理位置相近"));
                    matchResults.add(result);
                }
            }

            return success(matchResults);
        } catch (Exception e) {
            logger.error("匹配零工失败", e);
            return error("匹配服务暂时不可用，请稍后重试");
        }
    }

    /**
     * 计算招聘信息与零工的相似度（公开接口）
     */
    @GetMapping("/postings/{jobId}/similarity/{workerId}")
    public AjaxResult calculateJobWorkerSimilarity(@PathVariable Long jobId, @PathVariable Long workerId) {
        Double similarity = jobPostingService.calculateJobWorkerSimilarity(jobId, workerId);
        if (similarity == null) {
            return error("计算相似度失败，请检查招聘信息或零工信息是否存在");
        }
        
        return success()
                .put("jobId", jobId)
                .put("workerId", workerId)
                .put("similarity", similarity)
                .put("similarityPercentage", Math.round(similarity * 100));
    }

    /**
     * 查询相似的招聘信息（公开接口）
     */
    @GetMapping("/postings/{jobId}/similar")
    public AjaxResult getSimilarJobPostings(@PathVariable Long jobId, @RequestParam(defaultValue = "5") Integer limit) {
        JobPosting jobPosting = jobPostingService.selectJobPostingByJobId(jobId);
        if (jobPosting == null) {
            return error("招聘信息不存在");
        }
        List<JobPosting> list = jobPostingService.selectSimilarJobPostingList(jobPosting, limit);
        return success(list);
    }

    /**
     * 查询活跃的零工信息列表（公开接口）
     */
    @GetMapping("/workers")
    public TableDataInfo getActiveWorkers(WorkerProfile workerProfile) {
        startPage();
        List<WorkerProfile> list = workerProfileService.selectActiveWorkerProfileList(workerProfile);
        return getDataTable(list);
    }

    /**
     * 查询已验证的零工信息列表（公开接口）
     */
    @GetMapping("/workers/verified")
    public TableDataInfo getVerifiedWorkers(WorkerProfile workerProfile) {
        startPage();
        List<WorkerProfile> list = workerProfileService.selectVerifiedWorkerProfileList(workerProfile);
        return getDataTable(list);
    }

    /**
     * 查询高评分零工信息（公开接口）
     */
    @GetMapping("/workers/high-rated")
    public AjaxResult getHighRatedWorkers(@RequestParam(defaultValue = "4.0") Double minRating, 
                                         @RequestParam(defaultValue = "10") Integer limit) {
        List<WorkerProfile> list = workerProfileService.selectHighRatedWorkerProfileList(minRating, limit);
        return success(list);
    }

    /**
     * 查询经验丰富的零工信息（公开接口）
     */
    @GetMapping("/workers/experienced")
    public AjaxResult getExperiencedWorkers(@RequestParam(defaultValue = "3") Integer minExperience, 
                                           @RequestParam(defaultValue = "10") Integer limit) {
        List<WorkerProfile> list = workerProfileService.selectExperiencedWorkerProfileList(minExperience, limit);
        return success(list);
    }

    /**
     * 查询推荐零工信息（公开接口）
     */
    @GetMapping("/workers/recommended")
    public AjaxResult getRecommendedWorkers(@RequestParam(defaultValue = "10") Integer limit) {
        List<WorkerProfile> list = workerProfileService.selectRecommendedWorkerProfileList(limit);
        return success(list);
    }

    /**
     * 根据关键词搜索零工信息（公开接口）
     */
    @GetMapping("/workers/search")
    public TableDataInfo searchWorkers(@RequestParam String keyword) {
        startPage();
        List<WorkerProfile> list = workerProfileService.selectWorkerProfileByKeyword(keyword);
        return getDataTable(list);
    }

    /**
     * 根据技能匹配零工信息（公开接口）
     */
    @GetMapping("/workers/match-skills")
    public AjaxResult matchWorkersBySkills(@RequestParam List<String> skills, 
                                          @RequestParam(defaultValue = "10") Integer limit) {
        List<WorkerProfile> list = workerProfileService.selectWorkerProfileBySkills(skills, limit);
        return success(list);
    }

    /**
     * 根据地理位置匹配零工信息（公开接口）
     */
    @GetMapping("/workers/match-location")
    public AjaxResult matchWorkersByLocation(@RequestParam String location, 
                                            @RequestParam(defaultValue = "10.0") Double radius, 
                                            @RequestParam(defaultValue = "10") Integer limit) {
        List<WorkerProfile> list = workerProfileService.selectWorkerProfileByLocation(location, radius, limit);
        return success(list);
    }

    /**
     * 获取零工信息详细信息（公开接口）
     */
    @GetMapping("/workers/{workerId}")
    public AjaxResult getWorkerDetail(@PathVariable("workerId") Long workerId) {
        WorkerProfile workerProfile = workerProfileService.selectWorkerProfileDetailByWorkerId(workerId);
        if (workerProfile == null) {
            return error("零工信息不存在");
        }
        return success(workerProfile);
    }

    /**
     * 查询相似的零工信息（公开接口）
     */
    @GetMapping("/workers/{workerId}/similar")
    public AjaxResult getSimilarWorkers(@PathVariable Long workerId, @RequestParam(defaultValue = "5") Integer limit) {
        WorkerProfile workerProfile = workerProfileService.selectWorkerProfileByWorkerId(workerId);
        if (workerProfile == null) {
            return error("零工信息不存在");
        }
        List<WorkerProfile> list = workerProfileService.selectSimilarWorkerProfileList(workerProfile, limit);
        return success(list);
    }

    /**
     * 根据招聘信息统计数据（公开接口）
     */
    @GetMapping("/statistics/postings")
    public AjaxResult getJobPostingStatistics() {
        Map<String, Object> data = jobPostingService.selectJobPostingStatistics(null);
        return success(data);
    }

    /**
     * 根据工作类别统计招聘信息数量（公开接口）
     */
    @GetMapping("/statistics/postings/category")
    public AjaxResult getJobPostingStatisticsByCategory() {
        List<Map<String, Object>> data = jobPostingService.selectJobPostingCountByCategory();
        return success(data);
    }

    /**
     * 根据工作地点统计招聘信息数量（公开接口）
     */
    @GetMapping("/statistics/postings/location")
    public AjaxResult getJobPostingStatisticsByLocation() {
        List<Map<String, Object>> data = jobPostingService.selectJobPostingCountByLocation();
        return success(data);
    }

    /**
     * 根据零工统计数据（公开接口）
     */
    @GetMapping("/statistics/workers")
    public AjaxResult getWorkerStatistics() {
        Map<String, Object> data = workerProfileService.selectWorkerProfileStatistics(null);
        return success(data);
    }

    /**
     * 根据工作类别统计零工数量（公开接口）
     */
    @GetMapping("/statistics/workers/category")
    public AjaxResult getWorkerStatisticsByCategory() {
        List<Map<String, Object>> data = workerProfileService.selectWorkerProfileCountByCategory();
        return success(data);
    }

    /**
     * 根据所在地统计零工数量（公开接口）
     */
    @GetMapping("/statistics/workers/location")
    public AjaxResult getWorkerStatisticsByLocation() {
        List<Map<String, Object>> data = workerProfileService.selectWorkerProfileCountByLocation();
        return success(data);
    }

    /**
     * 快速匹配接口 - 基于简单条件快速匹配
     */
    @GetMapping("/quick-match")
    public AjaxResult quickMatch(@RequestParam(required = false) String jobType,
                                @RequestParam(required = false) String location,
                                @RequestParam(required = false) String skills,
                                @RequestParam(defaultValue = "5") Integer limit) {
        try {
            Map<String, Object> result = new java.util.HashMap<>();

            // 根据条件查找招聘信息
            JobPosting searchJob = new JobPosting();
            if (jobType != null && !jobType.isEmpty()) {
                searchJob.setJobType(jobType);
            }
            if (location != null && !location.isEmpty()) {
                searchJob.setWorkLocation(location);
            }

            List<JobPosting> matchedJobs = jobPostingService.selectPublishedJobPostingList(searchJob);

            // 限制结果数量
            if (matchedJobs.size() > limit) {
                matchedJobs = matchedJobs.subList(0, limit);
            }

            // 为每个招聘信息添加匹配度信息
            List<Map<String, Object>> jobsWithMatch = new java.util.ArrayList<>();
            for (JobPosting job : matchedJobs) {
                Map<String, Object> jobData = new java.util.HashMap<>();
                jobData.put("job", job);
                jobData.put("matchScore", calculateQuickMatchScore(job, jobType, location, skills));
                jobData.put("matchReasons", getQuickMatchReasons(job, jobType, location, skills));
                jobsWithMatch.add(jobData);
            }

            // 按匹配度排序
            jobsWithMatch.sort((a, b) -> {
                Double scoreA = (Double) a.get("matchScore");
                Double scoreB = (Double) b.get("matchScore");
                return scoreB.compareTo(scoreA);
            });

            result.put("jobs", jobsWithMatch);
            result.put("total", matchedJobs.size());
            result.put("searchCriteria", Map.of(
                "jobType", jobType != null ? jobType : "不限",
                "location", location != null ? location : "不限",
                "skills", skills != null ? skills : "不限"
            ));

            return success(result);
        } catch (Exception e) {
            logger.error("快速匹配失败", e);
            return error("快速匹配服务暂时不可用");
        }
    }

    /**
     * 计算快速匹配分数
     */
    private double calculateQuickMatchScore(JobPosting job, String jobType, String location, String skills) {
        double score = 0.5; // 基础分数

        // 工作类型匹配
        if (jobType != null && jobType.equals(job.getJobType())) {
            score += 0.3;
        }

        // 地点匹配
        if (location != null && job.getWorkLocation() != null &&
            job.getWorkLocation().contains(location)) {
            score += 0.2;
        }


        return Math.min(1.0, score);
    }

    /**
     * 获取快速匹配原因
     */
    private java.util.List<String> getQuickMatchReasons(JobPosting job, String jobType, String location, String skills) {
        java.util.List<String> reasons = new java.util.ArrayList<>();

        if (jobType != null && jobType.equals(job.getJobType())) {
            reasons.add("工作类型匹配");
        }

        if (location != null && job.getWorkLocation() != null &&
            job.getWorkLocation().contains(location)) {
            reasons.add("工作地点匹配");
        }



        if (reasons.isEmpty()) {
            reasons.add("基础匹配");
        }

        return reasons;
    }
}
