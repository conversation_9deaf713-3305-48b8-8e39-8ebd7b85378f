<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="培训订单" prop="orderId">
        <el-select v-model="queryParams.orderId" placeholder="请选择培训订单" clearable>
          <el-option
            v-for="order in orderOptions"
            :key="order.orderId"
            :label="order.orderTitle"
            :value="order.orderId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="机构名称" prop="institutionId">
        <el-select v-model="queryParams.institutionId" placeholder="请选择培训机构" clearable>
          <el-option
            v-for="institution in institutionOptions"
            :key="institution.institutionId"
            :label="institution.institutionName"
            :value="institution.institutionId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="申请状态" prop="applicationStatus">
        <el-select v-model="queryParams.applicationStatus" placeholder="请选择申请状态" clearable>
          <el-option label="待审核" value="PENDING" />
          <el-option label="已通过" value="APPROVED" />
          <el-option label="已拒绝" value="REJECTED" />
          <el-option label="已取消" value="CANCELLED" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Check"
          :disabled="multiple"
          @click="handleBatchApprove"
          v-hasPermi="['training:recruitment:review']"
        >批量通过</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Close"
          :disabled="multiple"
          @click="handleBatchReject"
          v-hasPermi="['training:recruitment:review']"
        >批量拒绝</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['training:recruitment:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="recruitmentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="申请ID" align="center" prop="applicationId" width="80" />
      <el-table-column label="培训订单" align="center" prop="orderTitle" :show-overflow-tooltip="true" width="200" />
      <el-table-column label="机构名称" align="center" prop="institutionName" :show-overflow-tooltip="true" width="180" />
      <el-table-column label="联系人" align="center" prop="contactPerson" width="100" />
      <el-table-column label="联系电话" align="center" prop="contactPhone" width="120" />
      <el-table-column label="培训方式" align="center" prop="trainingMethod" width="100" />
      <el-table-column label="报价(元)" align="center" prop="proposedFee" width="100">
        <template #default="scope">
          <span>{{ scope.row.proposedFee ? '￥' + scope.row.proposedFee : '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请状态" align="center" prop="applicationStatus" width="100">
        <template #default="scope">
          <el-tag
            :type="getStatusTagType(scope.row.applicationStatus)"
            disable-transitions
          >
            {{ getStatusText(scope.row.applicationStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="申请时间" align="center" prop="applicationTime" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.applicationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审核人" align="center" prop="reviewer" width="100" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['training:recruitment:query']">详情</el-button>
          <el-button 
            link 
            type="success" 
            icon="Check" 
            @click="handleApprove(scope.row)" 
            v-hasPermi="['training:recruitment:review']"
            v-if="scope.row.applicationStatus === 'PENDING'"
          >通过</el-button>
          <el-button 
            link 
            type="danger" 
            icon="Close" 
            @click="handleReject(scope.row)" 
            v-hasPermi="['training:recruitment:review']"
            v-if="scope.row.applicationStatus === 'PENDING'"
          >拒绝</el-button>
          <el-button 
            link 
            type="warning" 
            icon="Star" 
            @click="handleSelect(scope.row)" 
            v-hasPermi="['training:recruitment:select']"
            v-if="scope.row.applicationStatus === 'APPROVED'"
          >选中</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 申请详情对话框 -->
    <el-dialog title="申请详情" v-model="detailOpen" width="1000px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="申请ID">{{ detailForm.applicationId }}</el-descriptions-item>
        <el-descriptions-item label="培训订单">{{ detailForm.orderTitle }}</el-descriptions-item>
        <el-descriptions-item label="机构名称">{{ detailForm.institutionName }}</el-descriptions-item>
        <el-descriptions-item label="联系人">{{ detailForm.contactPerson }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ detailForm.contactPhone }}</el-descriptions-item>
        <el-descriptions-item label="培训方式">{{ detailForm.trainingMethod }}</el-descriptions-item>
        <el-descriptions-item label="报价">{{ detailForm.proposedFee ? '￥' + detailForm.proposedFee : '--' }}</el-descriptions-item>
        <el-descriptions-item label="申请状态">
          <el-tag :type="getStatusTagType(detailForm.applicationStatus)">
            {{ getStatusText(detailForm.applicationStatus) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="申请时间" :span="2">{{ parseTime(detailForm.applicationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="培训计划" :span="2">
          <div style="white-space: pre-wrap;">{{ detailForm.trainingPlan }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="培训大纲" :span="2">
          <div style="white-space: pre-wrap;">{{ detailForm.trainingOutline }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="师资信息" :span="2">
          <div style="white-space: pre-wrap;">{{ detailForm.teacherInfo }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="培训教材" :span="2">
          <div style="white-space: pre-wrap;">{{ detailForm.trainingMaterials }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="培训保障" :span="2">
          <div style="white-space: pre-wrap;">{{ detailForm.trainingGuarantee }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="成功案例" :span="2">
          <div style="white-space: pre-wrap;">{{ detailForm.successCases }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="审核人">{{ detailForm.reviewer }}</el-descriptions-item>
        <el-descriptions-item label="审核时间">{{ parseTime(detailForm.reviewTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="审核意见" :span="2">
          <div style="white-space: pre-wrap;">{{ detailForm.reviewComment }}</div>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog title="申请审核" v-model="reviewOpen" width="500px" append-to-body>
      <el-form ref="reviewRef" :model="reviewForm" :rules="reviewRules" label-width="80px">
        <el-form-item label="审核状态" prop="applicationStatus">
          <el-radio-group v-model="reviewForm.applicationStatus">
            <el-radio label="APPROVED">通过</el-radio>
            <el-radio label="REJECTED">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核意见" prop="reviewComment">
          <el-input v-model="reviewForm.reviewComment" type="textarea" placeholder="请输入审核意见" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitReview">确 定</el-button>
          <el-button @click="reviewOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
