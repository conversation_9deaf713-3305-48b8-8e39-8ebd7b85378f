import { parseTime } from "@/utils/ruoyi";

export const createJobPostingTableOption = (proxy) => {
    const {
        sys_normal_disable,
        job_type_options,
        job_category_options,
        salary_type_options,
        urgency_level_options,
        publisher_type_options,
        gender_requirement_options
    } = proxy.useDict(
        "sys_normal_disable",
        "job_type_options", 
        "job_category_options",
        "salary_type_options",
        "urgency_level_options",
        "publisher_type_options",
        "gender_requirement_options"
    );

    // 工作类型选项
    const jobTypeOptions = [
        { label: "全职", value: "全职" },
        { label: "兼职", value: "兼职" },
        { label: "临时工", value: "临时工" },
        { label: "小时工", value: "小时工" },
        { label: "实习", value: "实习" }
    ];

    // 工作类别选项
    const jobCategoryOptions = [
        { label: "服务员", value: "服务员" },
        { label: "保洁", value: "保洁" },
        { label: "搬运工", value: "搬运工" },
        { label: "销售", value: "销售" },
        { label: "客服", value: "客服" },
        { label: "配送员", value: "配送员" },
        { label: "厨师", value: "厨师" },
        { label: "司机", value: "司机" },
        { label: "保安", value: "保安" },
        { label: "其他", value: "其他" }
    ];

    // 薪资类型选项
    const salaryTypeOptions = [
        { label: "小时", value: "hourly" },
        { label: "日薪", value: "daily" },
        { label: "月薪", value: "monthly" },
        { label: "计件", value: "piece" }
    ];

    // 紧急程度选项
    const urgencyLevelOptions = [
        { label: "紧急", value: "urgent" },
        { label: "高", value: "high" },
        { label: "普通", value: "normal" },
        { label: "低", value: "low" }
    ];

    // 发布者类型选项
    const publisherTypeOptions = [
        { label: "雇主", value: "employer" },
        { label: "中介", value: "agency" },
        { label: "个人", value: "individual" }
    ];

    // 性别要求选项
    const genderRequirementOptions = [
        { label: "不限", value: "any" },
        { label: "男", value: "male" },
        { label: "女", value: "female" }
    ];

    // 状态选项
    const statusOptions = [
        { label: "草稿", value: "draft" },
        { label: "已发布", value: "published" },
        { label: "已暂停", value: "paused" },
        { label: "已关闭", value: "closed" },
        { label: "已完成", value: "completed" }
    ];

    return {
        dialogWidth: '1000px',  // 弹窗宽度（优化：减小宽度）
        dialogHeight: '65vh',   // 弹窗内容区最大高度（优化：减小高度）
        labelWidth: '120px',
        column: [
            // ==================== 核心匹配信息分组（优化：突出核心字段） ====================
            {
                label: "核心匹配信息",
                prop: "divider_core_info",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true
            },
            {
                label: "职位名称",
                prop: "jobTitle",
                search: true,
                searchSpan: 8,
                minWidth: 200,
                rules: [
                    { required: true, message: "职位名称不能为空", trigger: "blur" },
                    { min: 2, max: 200, message: "职位名称长度必须介于 2 和 200 之间", trigger: "blur" }
                ],
                span: 12
            },
            {
                label: "工作类型",
                prop: "jobType",
                search: true,
                searchSpan: 6,
                width: 120,
                align: "center",
                type: "select",
                dicData: jobTypeOptions,
                span: 6,
                slot: true,
                rules: [
                    { required: true, message: "工作类型不能为空", trigger: "change" }
                ]
            },
            {
                label: "工作类别",
                prop: "jobCategory",
                search: true,
                searchSpan: 6,
                width: 120,
                align: "center",
                type: "select",
                dicData: jobCategoryOptions,
                span: 6,
                slot: true,
                rules: [
                    { required: true, message: "工作类别不能为空", trigger: "change" }
                ]
            },
            {
                label: "工作地点",
                prop: "workLocation",
                search: true,
                searchSpan: 12,
                minWidth: 150,
                span: 12,
                rules: [
                    { required: true, message: "工作地点不能为空", trigger: "blur" },
                    { max: 200, message: "工作地点不能超过200个字符", trigger: "blur" }
                ]
            },
            {
                label: "详细地址",
                prop: "workAddress",
                search: false,
                span: 24,
                rules: [
                    { max: 500, message: "详细地址不能超过500个字符", trigger: "blur" }
                ]
            },
            {
                label: "职位描述",
                prop: "jobDescription",
                search: false,
                type: "textarea",
                span: 24,
                minRows: 4,
                maxRows: 8,
                showWordLimit: true,
                maxlength: 2000
            },
            {
                label: "薪资类型",
                prop: "salaryType",
                search: true,
                searchSpan: 6,
                width: 100,
                align: "center",
                type: "select",
                dicData: salaryTypeOptions,
                span: 6,
                rules: [
                    { required: true, message: "薪资类型不能为空", trigger: "change" }
                ]
            },
            {
                label: "最低薪资",
                prop: "salaryMin",
                search: false,
                type: "number",
                span: 8,
                precision: 2,
                min: 0,
                rules: [
                    { type: 'number', min: 0, message: '最低薪资不能小于0', trigger: 'blur' }
                ]
            },
            {
                label: "最高薪资",
                prop: "salaryMax",
                search: false,
                type: "number",
                span: 8,
                precision: 2,
                min: 0,
                rules: [
                    { type: 'number', min: 0, message: '最高薪资不能小于0', trigger: 'blur' }
                ]
            },
            {
                label: "学历要求",
                prop: "educationRequired",
                search: true,
                searchSpan: 8,
                width: 100,
                align: "center",
                span: 6,
                rules: [
                    { max: 50, message: "学历要求不能超过50个字符", trigger: "blur" }
                ]
            },
            // ==================== 基础信息分组（优化：合并相关字段） ====================
            {
                label: "基础信息",
                prop: "divider_basic_info",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true
            },
            {
                label: "工作地点",
                prop: "workLocation",
                search: true,
                searchSpan: 8,
                minWidth: 150,
                span: 12,
                rules: [
                    { required: true, message: "工作地点不能为空", trigger: "blur" },
                    { max: 200, message: "工作地点不能超过200个字符", trigger: "blur" }
                ]
            },
            {
                label: "招聘人数",
                prop: "positionsCount",
                search: false,
                width: 120,
                align: "center",
                slot: true,
                span: 6,
                addDisplay: false,
                editDisplay: false
            },
            {
                label: "紧急程度",
                prop: "urgencyLevel",
                search: true,
                searchSpan: 6,
                width: 100,
                align: "center",
                type: "select",
                dicData: urgencyLevelOptions,
                span: 6,
                slot: true,
                value: "normal"
            },
            {
                label: "职位描述",
                prop: "jobDescription",
                search: false,
                type: "textarea",
                span: 24,
                minRows: 3,
                maxRows: 6,
                showWordLimit: true,
                maxlength: 1000
            },
            {
                label: "经验要求",
                prop: "experienceRequired",
                search: false,
                span: 12,
                rules: [
                    { max: 100, message: "经验要求不能超过100个字符", trigger: "blur" }
                ]
            },
            // ==================== 联系信息分组（优化：简化联系信息） ====================
            {
                label: "联系信息",
                prop: "divider_contact_info",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true
            },
            {
                label: "联系人",
                prop: "contactPerson",
                search: false,
                span: 12,
                rules: [
                    { max: 100, message: "联系人不能超过100个字符", trigger: "blur" }
                ]
            },
            {
                label: "联系电话",
                prop: "contactPhone",
                search: false,
                span: 12,
                rules: [
                    { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
                ]
            },
            {
                label: "公司名称",
                prop: "companyName",
                search: true,
                searchSpan: 8,
                span: 24,
                rules: [
                    { max: 200, message: "公司名称不能超过200个字符", trigger: "blur" }
                ]
            },
            // ==================== 状态信息分组（优化：简化状态信息） ====================
            {
                label: "状态信息",
                prop: "divider_status_info",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true,
                addDisplay: false,
                editDisplay: false
            },
            {
                label: "招聘人数",
                prop: "positionsAvailable",
                search: false,
                type: "number",
                span: 8,
                min: 1,
                value: 1,
                rules: [
                    { required: true, message: "招聘人数不能为空", trigger: "blur" },
                    { type: 'number', min: 1, message: '招聘人数不能小于1', trigger: 'blur' }
                ]
            },
            {
                label: "已招聘人数",
                prop: "positionsFilled",
                search: false,
                type: "number",
                span: 8,
                min: 0,
                value: 0,
                disabled: true
            },
            {
                label: "状态",
                prop: "status",
                search: true,
                searchSpan: 6,
                width: 100,
                align: "center",
                type: "select",
                dicData: statusOptions,
                span: 8,
                slot: true,
                addDisplay: false,
                editDisplay: false
            },
            {
                label: "浏览申请",
                prop: "viewApplication",
                search: false,
                width: 120,
                align: "center",
                slot: true,
                span: 8,
                addDisplay: false,
                editDisplay: false
            },
            {
                label: "创建时间",
                prop: "createTime",
                search: true,
                searchSpan: 8,
                searchType: "daterange",
                searchFormat: "YYYY-MM-DD",
                searchValueFormat: "YYYY-MM-DD",
                width: 180,
                align: "center",
                formatter: (row, column, cellValue) => {
                    return parseTime(cellValue, '{y}-{m}-{d} {h}:{i}:{s}')
                },
                span: 8,
                addDisplay: false,
                editDisplay: false
            },
            {
                label: "备注",
                prop: "remark",
                search: false,
                type: "textarea",
                span: 24,
                minRows: 2,
                maxRows: 4,
                showWordLimit: true,
                maxlength: 500
            }
        ]
    };
};
