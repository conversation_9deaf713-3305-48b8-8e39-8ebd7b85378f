<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="机构名称" prop="institutionName">
        <el-input
          v-model="queryParams.institutionName"
          placeholder="请输入机构名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="机构编码" prop="institutionCode">
        <el-input
          v-model="queryParams.institutionCode"
          placeholder="请输入机构编码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="机构类型" prop="institutionType">
        <el-select v-model="queryParams.institutionType" placeholder="请选择机构类型" clearable>
          <el-option label="企业培训机构" value="企业培训机构" />
          <el-option label="职业院校" value="职业院校" />
          <el-option label="高等院校" value="高等院校" />
          <el-option label="政府机构" value="政府机构" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="待审核" value="0" />
          <el-option label="已认证" value="1" />
          <el-option label="已拒绝" value="2" />
          <el-option label="已禁用" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['training:institution:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['training:institution:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['training:institution:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['training:institution:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="institutionList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="机构ID" align="center" prop="institutionId" />
      <el-table-column label="机构名称" align="center" prop="institutionName" :show-overflow-tooltip="true" />
      <el-table-column label="机构编码" align="center" prop="institutionCode" />
      <el-table-column label="联系人" align="center" prop="contactPerson" />
      <el-table-column label="联系电话" align="center" prop="contactPhone" />
      <el-table-column label="机构类型" align="center" prop="institutionType" />
      <el-table-column label="资质等级" align="center" prop="qualificationLevel" />
      <el-table-column label="师资数量" align="center" prop="teacherCount" />
      <el-table-column label="培训能力" align="center" prop="trainingCapacity">
        <template #default="scope">
          <span>{{ scope.row.trainingCapacity }}人次/年</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="sys_institution_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['training:institution:query']">详情</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['training:institution:edit']">修改</el-button>
          <el-button link type="primary" icon="Check" @click="handleAudit(scope.row)" v-hasPermi="['training:institution:audit']">审核</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['training:institution:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改培训机构信息对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="institutionRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="机构名称" prop="institutionName">
              <el-input v-model="form.institutionName" placeholder="请输入机构名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="机构编码" prop="institutionCode">
              <el-input v-model="form.institutionCode" placeholder="请输入机构编码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="法人代表" prop="legalPerson">
              <el-input v-model="form.legalPerson" placeholder="请输入法人代表" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人" prop="contactPerson">
              <el-input v-model="form.contactPerson" placeholder="请输入联系人" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="contactPhone">
              <el-input v-model="form.contactPhone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系邮箱" prop="contactEmail">
              <el-input v-model="form.contactEmail" placeholder="请输入联系邮箱" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="机构地址" prop="institutionAddress">
          <el-input v-model="form.institutionAddress" type="textarea" placeholder="请输入机构地址" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="营业执照号" prop="businessLicense">
              <el-input v-model="form.businessLicense" placeholder="请输入营业执照号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="办学许可证号" prop="educationLicense">
              <el-input v-model="form.educationLicense" placeholder="请输入办学许可证号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="成立时间" prop="establishedDate">
              <el-date-picker clearable
                v-model="form.establishedDate"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择成立时间">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="注册资本" prop="registeredCapital">
              <el-input v-model="form.registeredCapital" placeholder="请输入注册资本(万元)" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="经营范围" prop="businessScope">
          <el-input v-model="form.businessScope" type="textarea" placeholder="请输入经营范围" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="机构类型" prop="institutionType">
              <el-select v-model="form.institutionType" placeholder="请选择机构类型">
                <el-option label="企业培训机构" value="企业培训机构" />
                <el-option label="职业院校" value="职业院校" />
                <el-option label="高等院校" value="高等院校" />
                <el-option label="政府机构" value="政府机构" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资质等级" prop="qualificationLevel">
              <el-select v-model="form.qualificationLevel" placeholder="请选择资质等级">
                <el-option label="A级" value="A级" />
                <el-option label="B级" value="B级" />
                <el-option label="C级" value="C级" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="师资数量" prop="teacherCount">
              <el-input v-model="form.teacherCount" placeholder="请输入师资数量" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="培训能力" prop="trainingCapacity">
              <el-input v-model="form.trainingCapacity" placeholder="请输入年培训能力(人次)" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="专业领域" prop="specialties">
          <el-input v-model="form.specialties" type="textarea" placeholder="请输入专业领域，多个用逗号分隔" />
        </el-form-item>
        <el-form-item label="机构简介" prop="institutionIntro">
          <el-input v-model="form.institutionIntro" type="textarea" placeholder="请输入机构简介" />
        </el-form-item>
        <el-form-item label="官网地址" prop="websiteUrl">
          <el-input v-model="form.websiteUrl" placeholder="请输入官网地址" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 机构详情对话框 -->
    <el-dialog title="机构详情" v-model="detailOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="机构名称">{{ detailForm.institutionName }}</el-descriptions-item>
        <el-descriptions-item label="机构编码">{{ detailForm.institutionCode }}</el-descriptions-item>
        <el-descriptions-item label="法人代表">{{ detailForm.legalPerson }}</el-descriptions-item>
        <el-descriptions-item label="联系人">{{ detailForm.contactPerson }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ detailForm.contactPhone }}</el-descriptions-item>
        <el-descriptions-item label="联系邮箱">{{ detailForm.contactEmail }}</el-descriptions-item>
        <el-descriptions-item label="机构地址" :span="2">{{ detailForm.institutionAddress }}</el-descriptions-item>
        <el-descriptions-item label="营业执照号">{{ detailForm.businessLicense }}</el-descriptions-item>
        <el-descriptions-item label="办学许可证号">{{ detailForm.educationLicense }}</el-descriptions-item>
        <el-descriptions-item label="成立时间">{{ detailForm.establishedDate }}</el-descriptions-item>
        <el-descriptions-item label="注册资本">{{ detailForm.registeredCapital }}万元</el-descriptions-item>
        <el-descriptions-item label="机构类型">{{ detailForm.institutionType }}</el-descriptions-item>
        <el-descriptions-item label="资质等级">{{ detailForm.qualificationLevel }}</el-descriptions-item>
        <el-descriptions-item label="师资数量">{{ detailForm.teacherCount }}人</el-descriptions-item>
        <el-descriptions-item label="培训能力">{{ detailForm.trainingCapacity }}人次/年</el-descriptions-item>
        <el-descriptions-item label="经营范围" :span="2">{{ detailForm.businessScope }}</el-descriptions-item>
        <el-descriptions-item label="专业领域" :span="2">{{ detailForm.specialties }}</el-descriptions-item>
        <el-descriptions-item label="机构简介" :span="2">{{ detailForm.institutionIntro }}</el-descriptions-item>
        <el-descriptions-item label="官网地址" :span="2">
          <el-link :href="detailForm.websiteUrl" target="_blank" type="primary">{{ detailForm.websiteUrl }}</el-link>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <dict-tag :options="sys_institution_status" :value="detailForm.status"/>
        </el-descriptions-item>
        <el-descriptions-item label="审核人">{{ detailForm.auditor }}</el-descriptions-item>
        <el-descriptions-item label="审核时间" :span="2">{{ parseTime(detailForm.auditTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="审核意见" :span="2">{{ detailForm.auditComment }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog title="机构审核" v-model="auditOpen" width="500px" append-to-body>
      <el-form ref="auditRef" :model="auditForm" :rules="auditRules" label-width="80px">
        <el-form-item label="审核状态" prop="status">
          <el-radio-group v-model="auditForm.status">
            <el-radio label="1">通过</el-radio>
            <el-radio label="2">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核意见" prop="auditComment">
          <el-input v-model="auditForm.auditComment" type="textarea" placeholder="请输入审核意见" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitAudit">确 定</el-button>
          <el-button @click="auditOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="TrainingInstitution">
import { listTrainingInstitution, getTrainingInstitution, delTrainingInstitution, addTrainingInstitution, updateTrainingInstitution, auditTrainingInstitution } from "@/api/training/institution";

const { proxy } = getCurrentInstance();
const { sys_institution_status } = proxy.useDict('sys_institution_status');

const institutionList = ref([]);
const open = ref(false);
const detailOpen = ref(false);
const auditOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  detailForm: {},
  auditForm: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    institutionName: null,
    institutionCode: null,
    institutionType: null,
    status: null,
  },
  rules: {
    institutionName: [
      { required: true, message: "机构名称不能为空", trigger: "blur" }
    ],
    contactPerson: [
      { required: true, message: "联系人不能为空", trigger: "blur" }
    ],
    contactPhone: [
      { required: true, message: "联系电话不能为空", trigger: "blur" }
    ],
  },
  auditRules: {
    status: [
      { required: true, message: "请选择审核状态", trigger: "change" }
    ],
    auditComment: [
      { required: true, message: "审核意见不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, detailForm, auditForm, rules, auditRules } = toRefs(data);

/** 查询培训机构信息列表 */
function getList() {
  loading.value = true;
  listTrainingInstitution(queryParams.value).then(response => {
    institutionList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    institutionId: null,
    institutionName: null,
    institutionCode: null,
    legalPerson: null,
    contactPerson: null,
    contactPhone: null,
    contactEmail: null,
    institutionAddress: null,
    businessLicense: null,
    educationLicense: null,
    establishedDate: null,
    registeredCapital: null,
    businessScope: null,
    institutionType: null,
    qualificationLevel: null,
    teacherCount: null,
    trainingCapacity: null,
    specialties: null,
    institutionIntro: null,
    websiteUrl: null,
    status: "0",
    remark: null
  };
  proxy.resetForm("institutionRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.institutionId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加培训机构信息";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _institutionId = row.institutionId || ids.value
  getTrainingInstitution(_institutionId).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改培训机构信息";
  });
}

/** 详情按钮操作 */
function handleDetail(row) {
  const _institutionId = row.institutionId;
  getTrainingInstitution(_institutionId).then(response => {
    detailForm.value = response.data;
    detailOpen.value = true;
  });
}

/** 审核按钮操作 */
function handleAudit(row) {
  auditForm.value = {
    institutionId: row.institutionId,
    status: null,
    auditComment: null,
    auditor: proxy.$auth.user.nickName
  };
  auditOpen.value = true;
}

/** 提交审核 */
function submitAudit() {
  proxy.$refs["auditRef"].validate(valid => {
    if (valid) {
      auditTrainingInstitution(auditForm.value.institutionId, auditForm.value).then(response => {
        proxy.$modal.msgSuccess("审核成功");
        auditOpen.value = false;
        getList();
      });
    }
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["institutionRef"].validate(valid => {
    if (valid) {
      if (form.value.institutionId != null) {
        updateTrainingInstitution(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addTrainingInstitution(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _institutionIds = row.institutionId || ids.value;
  proxy.$modal.confirm('是否确认删除培训机构信息编号为"' + _institutionIds + '"的数据项？').then(function() {
    return delTrainingInstitution(_institutionIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('training/institution/export', {
    ...queryParams.value
  }, `institution_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
