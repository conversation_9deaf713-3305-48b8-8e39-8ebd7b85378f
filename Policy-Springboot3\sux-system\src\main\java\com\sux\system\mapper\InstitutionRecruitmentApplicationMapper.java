package com.sux.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sux.system.domain.InstitutionRecruitmentApplication;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 机构招募申请Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface InstitutionRecruitmentApplicationMapper extends BaseMapper<InstitutionRecruitmentApplication>
{
    /**
     * 查询机构招募申请
     * 
     * @param applicationId 机构招募申请主键
     * @return 机构招募申请
     */
    public InstitutionRecruitmentApplication selectInstitutionRecruitmentApplicationByApplicationId(Long applicationId);

    /**
     * 查询机构招募申请列表
     * 
     * @param institutionRecruitmentApplication 机构招募申请
     * @return 机构招募申请集合
     */
    public List<InstitutionRecruitmentApplication> selectInstitutionRecruitmentApplicationList(InstitutionRecruitmentApplication institutionRecruitmentApplication);

    /**
     * 新增机构招募申请
     * 
     * @param institutionRecruitmentApplication 机构招募申请
     * @return 结果
     */
    public int insertInstitutionRecruitmentApplication(InstitutionRecruitmentApplication institutionRecruitmentApplication);

    /**
     * 修改机构招募申请
     * 
     * @param institutionRecruitmentApplication 机构招募申请
     * @return 结果
     */
    public int updateInstitutionRecruitmentApplication(InstitutionRecruitmentApplication institutionRecruitmentApplication);

    /**
     * 删除机构招募申请
     * 
     * @param applicationId 机构招募申请主键
     * @return 结果
     */
    public int deleteInstitutionRecruitmentApplicationByApplicationId(Long applicationId);

    /**
     * 批量删除机构招募申请
     * 
     * @param applicationIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInstitutionRecruitmentApplicationByApplicationIds(Long[] applicationIds);

    /**
     * 根据订单ID查询申请列表
     * 
     * @param orderId 订单ID
     * @return 申请列表
     */
    public List<InstitutionRecruitmentApplication> selectApplicationsByOrderId(@Param("orderId") Long orderId);

    /**
     * 根据机构ID查询申请列表
     * 
     * @param institutionId 机构ID
     * @return 申请列表
     */
    public List<InstitutionRecruitmentApplication> selectApplicationsByInstitutionId(@Param("institutionId") Long institutionId);

    /**
     * 检查机构是否已申请该订单
     * 
     * @param orderId 订单ID
     * @param institutionId 机构ID
     * @return 申请记录
     */
    public InstitutionRecruitmentApplication checkApplicationExists(@Param("orderId") Long orderId, @Param("institutionId") Long institutionId);

    /**
     * 统计订单的申请数量
     * 
     * @param orderId 订单ID
     * @return 申请数量
     */
    public int countApplicationsByOrderId(@Param("orderId") Long orderId);

    /**
     * 统计机构的申请数量
     * 
     * @param institutionId 机构ID
     * @return 申请数量
     */
    public int countApplicationsByInstitutionId(@Param("institutionId") Long institutionId);

    /**
     * 根据状态查询申请列表
     * 
     * @param status 申请状态
     * @return 申请列表
     */
    public List<InstitutionRecruitmentApplication> selectApplicationsByStatus(@Param("status") String status);

    /**
     * 批量审核申请
     * 
     * @param applicationIds 申请ID数组
     * @param status 审核状态
     * @param reviewer 审核人
     * @param reviewComment 审核意见
     * @return 结果
     */
    public int batchReviewApplications(@Param("applicationIds") Long[] applicationIds, 
                                     @Param("status") String status, 
                                     @Param("reviewer") String reviewer, 
                                     @Param("reviewComment") String reviewComment);

    /**
     * 获取申请统计信息
     * 
     * @return 统计信息
     */
    public List<InstitutionRecruitmentApplication> selectApplicationStatistics();

    /**
     * 根据订单ID和状态查询申请列表
     * 
     * @param orderId 订单ID
     * @param status 申请状态
     * @return 申请列表
     */
    public List<InstitutionRecruitmentApplication> selectApplicationsByOrderIdAndStatus(@Param("orderId") Long orderId, @Param("status") String status);
}
