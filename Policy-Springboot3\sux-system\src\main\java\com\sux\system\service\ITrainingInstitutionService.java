package com.sux.system.service;

import com.sux.system.domain.TrainingInstitution;

import java.util.List;

/**
 * 培训机构信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface ITrainingInstitutionService 
{
    /**
     * 查询培训机构信息
     * 
     * @param institutionId 培训机构信息主键
     * @return 培训机构信息
     */
    public TrainingInstitution selectTrainingInstitutionByInstitutionId(Long institutionId);

    /**
     * 查询培训机构信息列表
     * 
     * @param trainingInstitution 培训机构信息
     * @return 培训机构信息集合
     */
    public List<TrainingInstitution> selectTrainingInstitutionList(TrainingInstitution trainingInstitution);

    /**
     * 新增培训机构信息
     * 
     * @param trainingInstitution 培训机构信息
     * @return 结果
     */
    public int insertTrainingInstitution(TrainingInstitution trainingInstitution);

    /**
     * 修改培训机构信息
     * 
     * @param trainingInstitution 培训机构信息
     * @return 结果
     */
    public int updateTrainingInstitution(TrainingInstitution trainingInstitution);

    /**
     * 批量删除培训机构信息
     * 
     * @param institutionIds 需要删除的培训机构信息主键集合
     * @return 结果
     */
    public int deleteTrainingInstitutionByInstitutionIds(Long[] institutionIds);

    /**
     * 删除培训机构信息信息
     * 
     * @param institutionId 培训机构信息主键
     * @return 结果
     */
    public int deleteTrainingInstitutionByInstitutionId(Long institutionId);

    /**
     * 检查机构编码是否唯一
     * 
     * @param trainingInstitution 机构信息
     * @return 结果
     */
    public boolean checkInstitutionCodeUnique(TrainingInstitution trainingInstitution);

    /**
     * 检查机构名称是否唯一
     * 
     * @param trainingInstitution 机构信息
     * @return 结果
     */
    public boolean checkInstitutionNameUnique(TrainingInstitution trainingInstitution);

    /**
     * 审核机构
     * 
     * @param institutionId 机构ID
     * @param status 审核状态
     * @param auditor 审核人
     * @param auditComment 审核意见
     * @return 结果
     */
    public int auditInstitution(Long institutionId, String status, String auditor, String auditComment);

    /**
     * 根据状态查询机构列表
     * 
     * @param status 状态
     * @return 机构列表
     */
    public List<TrainingInstitution> selectTrainingInstitutionByStatus(String status);

    /**
     * 根据专业领域查询机构列表
     * 
     * @param specialty 专业领域
     * @return 机构列表
     */
    public List<TrainingInstitution> selectTrainingInstitutionBySpecialty(String specialty);

    /**
     * 统计各状态机构数量
     * 
     * @return 统计结果
     */
    public List<TrainingInstitution> selectInstitutionStatusStatistics();

    /**
     * 批量审核机构
     * 
     * @param institutionIds 机构ID数组
     * @param status 审核状态
     * @param auditor 审核人
     * @param auditComment 审核意见
     * @return 结果
     */
    public int batchAuditInstitutions(Long[] institutionIds, String status, String auditor, String auditComment);
}
