package com.sux.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sux.common.annotation.Excel;
import com.sux.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 培训机构信息对象 training_institution
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@TableName("training_institution")
public class TrainingInstitution extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 机构ID */
    @TableId(type = IdType.AUTO)
    private Long institutionId;

    /** 机构名称 */
    @Excel(name = "机构名称")
    @NotBlank(message = "机构名称不能为空")
    @Size(min = 0, max = 200, message = "机构名称不能超过200个字符")
    private String institutionName;

    /** 机构编码 */
    @Excel(name = "机构编码")
    @Size(min = 0, max = 50, message = "机构编码不能超过50个字符")
    private String institutionCode;

    /** 法人代表 */
    @Excel(name = "法人代表")
    @Size(min = 0, max = 50, message = "法人代表不能超过50个字符")
    private String legalPerson;

    /** 联系人 */
    @Excel(name = "联系人")
    @NotBlank(message = "联系人不能为空")
    @Size(min = 0, max = 50, message = "联系人不能超过50个字符")
    private String contactPerson;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @NotBlank(message = "联系电话不能为空")
    @Size(min = 0, max = 20, message = "联系电话不能超过20个字符")
    private String contactPhone;

    /** 联系邮箱 */
    @Excel(name = "联系邮箱")
    @Size(min = 0, max = 100, message = "联系邮箱不能超过100个字符")
    private String contactEmail;

    /** 机构地址 */
    @Excel(name = "机构地址")
    @Size(min = 0, max = 500, message = "机构地址不能超过500个字符")
    private String institutionAddress;

    /** 营业执照号 */
    @Excel(name = "营业执照号")
    @Size(min = 0, max = 50, message = "营业执照号不能超过50个字符")
    private String businessLicense;

    /** 办学许可证号 */
    @Excel(name = "办学许可证号")
    @Size(min = 0, max = 50, message = "办学许可证号不能超过50个字符")
    private String educationLicense;

    /** 成立时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "成立时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date establishedDate;

    /** 注册资本(万元) */
    @Excel(name = "注册资本(万元)")
    private BigDecimal registeredCapital;

    /** 经营范围 */
    @Excel(name = "经营范围")
    private String businessScope;

    /** 机构类型 */
    @Excel(name = "机构类型")
    @Size(min = 0, max = 50, message = "机构类型不能超过50个字符")
    private String institutionType;

    /** 资质等级 */
    @Excel(name = "资质等级")
    @Size(min = 0, max = 20, message = "资质等级不能超过20个字符")
    private String qualificationLevel;

    /** 师资数量 */
    @Excel(name = "师资数量")
    private Integer teacherCount;

    /** 年培训能力(人次) */
    @Excel(name = "年培训能力(人次)")
    private Integer trainingCapacity;

    /** 专业领域(JSON格式存储) */
    @Excel(name = "专业领域")
    private String specialties;

    /** 机构简介 */
    @Excel(name = "机构简介")
    private String institutionIntro;

    /** 官网地址 */
    @Excel(name = "官网地址")
    @Size(min = 0, max = 200, message = "官网地址不能超过200个字符")
    private String websiteUrl;

    /** 机构LOGO地址 */
    @Excel(name = "机构LOGO地址")
    @Size(min = 0, max = 500, message = "机构LOGO地址不能超过500个字符")
    private String logoUrl;

    /** 状态(0待审核 1已认证 2已拒绝 3已禁用) */
    @Excel(name = "状态", readConverterExp = "0=待审核,1=已认证,2=已拒绝,3=已禁用")
    private String status;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /** 审核人 */
    @Excel(name = "审核人")
    @Size(min = 0, max = 50, message = "审核人不能超过50个字符")
    private String auditor;

    /** 审核意见 */
    @Excel(name = "审核意见")
    private String auditComment;

    public void setInstitutionId(Long institutionId) 
    {
        this.institutionId = institutionId;
    }

    public Long getInstitutionId() 
    {
        return institutionId;
    }
    public void setInstitutionName(String institutionName) 
    {
        this.institutionName = institutionName;
    }

    public String getInstitutionName() 
    {
        return institutionName;
    }
    public void setInstitutionCode(String institutionCode) 
    {
        this.institutionCode = institutionCode;
    }

    public String getInstitutionCode() 
    {
        return institutionCode;
    }
    public void setLegalPerson(String legalPerson) 
    {
        this.legalPerson = legalPerson;
    }

    public String getLegalPerson() 
    {
        return legalPerson;
    }
    public void setContactPerson(String contactPerson) 
    {
        this.contactPerson = contactPerson;
    }

    public String getContactPerson() 
    {
        return contactPerson;
    }
    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }
    public void setContactEmail(String contactEmail) 
    {
        this.contactEmail = contactEmail;
    }

    public String getContactEmail() 
    {
        return contactEmail;
    }
    public void setInstitutionAddress(String institutionAddress) 
    {
        this.institutionAddress = institutionAddress;
    }

    public String getInstitutionAddress() 
    {
        return institutionAddress;
    }
    public void setBusinessLicense(String businessLicense) 
    {
        this.businessLicense = businessLicense;
    }

    public String getBusinessLicense() 
    {
        return businessLicense;
    }
    public void setEducationLicense(String educationLicense) 
    {
        this.educationLicense = educationLicense;
    }

    public String getEducationLicense() 
    {
        return educationLicense;
    }
    public void setEstablishedDate(Date establishedDate) 
    {
        this.establishedDate = establishedDate;
    }

    public Date getEstablishedDate() 
    {
        return establishedDate;
    }
    public void setRegisteredCapital(BigDecimal registeredCapital) 
    {
        this.registeredCapital = registeredCapital;
    }

    public BigDecimal getRegisteredCapital() 
    {
        return registeredCapital;
    }
    public void setBusinessScope(String businessScope) 
    {
        this.businessScope = businessScope;
    }

    public String getBusinessScope() 
    {
        return businessScope;
    }
    public void setInstitutionType(String institutionType) 
    {
        this.institutionType = institutionType;
    }

    public String getInstitutionType() 
    {
        return institutionType;
    }
    public void setQualificationLevel(String qualificationLevel) 
    {
        this.qualificationLevel = qualificationLevel;
    }

    public String getQualificationLevel() 
    {
        return qualificationLevel;
    }
    public void setTeacherCount(Integer teacherCount) 
    {
        this.teacherCount = teacherCount;
    }

    public Integer getTeacherCount() 
    {
        return teacherCount;
    }
    public void setTrainingCapacity(Integer trainingCapacity) 
    {
        this.trainingCapacity = trainingCapacity;
    }

    public Integer getTrainingCapacity() 
    {
        return trainingCapacity;
    }
    public void setSpecialties(String specialties) 
    {
        this.specialties = specialties;
    }

    public String getSpecialties() 
    {
        return specialties;
    }
    public void setInstitutionIntro(String institutionIntro) 
    {
        this.institutionIntro = institutionIntro;
    }

    public String getInstitutionIntro() 
    {
        return institutionIntro;
    }
    public void setWebsiteUrl(String websiteUrl) 
    {
        this.websiteUrl = websiteUrl;
    }

    public String getWebsiteUrl() 
    {
        return websiteUrl;
    }
    public void setLogoUrl(String logoUrl) 
    {
        this.logoUrl = logoUrl;
    }

    public String getLogoUrl() 
    {
        return logoUrl;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setAuditTime(Date auditTime) 
    {
        this.auditTime = auditTime;
    }

    public Date getAuditTime() 
    {
        return auditTime;
    }
    public void setAuditor(String auditor) 
    {
        this.auditor = auditor;
    }

    public String getAuditor() 
    {
        return auditor;
    }
    public void setAuditComment(String auditComment) 
    {
        this.auditComment = auditComment;
    }

    public String getAuditComment() 
    {
        return auditComment;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("institutionId", getInstitutionId())
            .append("institutionName", getInstitutionName())
            .append("institutionCode", getInstitutionCode())
            .append("legalPerson", getLegalPerson())
            .append("contactPerson", getContactPerson())
            .append("contactPhone", getContactPhone())
            .append("contactEmail", getContactEmail())
            .append("institutionAddress", getInstitutionAddress())
            .append("businessLicense", getBusinessLicense())
            .append("educationLicense", getEducationLicense())
            .append("establishedDate", getEstablishedDate())
            .append("registeredCapital", getRegisteredCapital())
            .append("businessScope", getBusinessScope())
            .append("institutionType", getInstitutionType())
            .append("qualificationLevel", getQualificationLevel())
            .append("teacherCount", getTeacherCount())
            .append("trainingCapacity", getTrainingCapacity())
            .append("specialties", getSpecialties())
            .append("institutionIntro", getInstitutionIntro())
            .append("websiteUrl", getWebsiteUrl())
            .append("logoUrl", getLogoUrl())
            .append("status", getStatus())
            .append("auditTime", getAuditTime())
            .append("auditor", getAuditor())
            .append("auditComment", getAuditComment())
            .append("delFlag", getDelFlag())
            .append("createId", getCreateId())
            .append("createTime", getCreateTime())
            .append("updateId", getUpdateId())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
