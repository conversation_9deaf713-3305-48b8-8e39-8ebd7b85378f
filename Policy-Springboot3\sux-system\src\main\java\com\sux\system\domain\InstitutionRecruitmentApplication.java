package com.sux.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sux.common.annotation.Excel;
import com.sux.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 机构招募申请对象 institution_recruitment_application
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@TableName("institution_recruitment_application")
public class InstitutionRecruitmentApplication extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 申请ID */
    @TableId(type = IdType.AUTO)
    private Long applicationId;

    /** 培训订单ID */
    @Excel(name = "培训订单ID")
    @NotNull(message = "培训订单ID不能为空")
    private Long orderId;

    /** 机构ID */
    @Excel(name = "机构ID")
    @NotNull(message = "机构ID不能为空")
    private Long institutionId;

    /** 申请类型 */
    @Excel(name = "申请类型")
    @Size(min = 0, max = 20, message = "申请类型不能超过20个字符")
    private String applicationType;

    /** 培训计划详情 */
    @Excel(name = "培训计划详情")
    private String trainingPlan;

    /** 培训大纲 */
    @Excel(name = "培训大纲")
    private String trainingOutline;

    /** 师资信息(JSON格式) */
    @Excel(name = "师资信息")
    private String teacherInfo;

    /** 培训教材说明 */
    @Excel(name = "培训教材说明")
    private String trainingMaterials;

    /** 培训方式 */
    @Excel(name = "培训方式")
    @Size(min = 0, max = 100, message = "培训方式不能超过100个字符")
    private String trainingMethod;

    /** 报价(元) */
    @Excel(name = "报价(元)")
    private BigDecimal proposedFee;

    /** 培训保障措施 */
    @Excel(name = "培训保障措施")
    private String trainingGuarantee;

    /** 成功案例 */
    @Excel(name = "成功案例")
    private String successCases;

    /** 申请状态 */
    @Excel(name = "申请状态", readConverterExp = "PENDING=待审核,APPROVED=已通过,REJECTED=已拒绝,CANCELLED=已取消")
    @Size(min = 0, max = 20, message = "申请状态不能超过20个字符")
    private String applicationStatus;

    /** 申请时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "申请时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "申请时间不能为空")
    private Date applicationTime;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date reviewTime;

    /** 审核人 */
    @Excel(name = "审核人")
    @Size(min = 0, max = 50, message = "审核人不能超过50个字符")
    private String reviewer;

    /** 审核意见 */
    @Excel(name = "审核意见")
    private String reviewComment;

    /** 合同状态 */
    @Excel(name = "合同状态", readConverterExp = "DRAFT=草稿,SIGNED=已签署,EXECUTED=执行中,COMPLETED=已完成,TERMINATED=已终止")
    @Size(min = 0, max = 20, message = "合同状态不能超过20个字符")
    private String contractStatus;

    /** 合同金额 */
    @Excel(name = "合同金额")
    private BigDecimal contractAmount;

    /** 合同文件地址 */
    @Excel(name = "合同文件地址")
    @Size(min = 0, max = 500, message = "合同文件地址不能超过500个字符")
    private String contractFileUrl;

    /** 履约评分(1-5分) */
    @Excel(name = "履约评分")
    private BigDecimal performanceScore;

    /** 履约评价 */
    @Excel(name = "履约评价")
    private String performanceComment;

    // 关联查询字段
    /** 培训订单标题 */
    private String orderTitle;

    /** 机构名称 */
    private String institutionName;

    /** 机构联系人 */
    private String contactPerson;

    /** 机构联系电话 */
    private String contactPhone;

    public void setApplicationId(Long applicationId) 
    {
        this.applicationId = applicationId;
    }

    public Long getApplicationId() 
    {
        return applicationId;
    }
    public void setOrderId(Long orderId) 
    {
        this.orderId = orderId;
    }

    public Long getOrderId() 
    {
        return orderId;
    }
    public void setInstitutionId(Long institutionId) 
    {
        this.institutionId = institutionId;
    }

    public Long getInstitutionId() 
    {
        return institutionId;
    }
    public void setApplicationType(String applicationType) 
    {
        this.applicationType = applicationType;
    }

    public String getApplicationType() 
    {
        return applicationType;
    }
    public void setTrainingPlan(String trainingPlan) 
    {
        this.trainingPlan = trainingPlan;
    }

    public String getTrainingPlan() 
    {
        return trainingPlan;
    }
    public void setTrainingOutline(String trainingOutline) 
    {
        this.trainingOutline = trainingOutline;
    }

    public String getTrainingOutline() 
    {
        return trainingOutline;
    }
    public void setTeacherInfo(String teacherInfo) 
    {
        this.teacherInfo = teacherInfo;
    }

    public String getTeacherInfo() 
    {
        return teacherInfo;
    }
    public void setTrainingMaterials(String trainingMaterials) 
    {
        this.trainingMaterials = trainingMaterials;
    }

    public String getTrainingMaterials() 
    {
        return trainingMaterials;
    }
    public void setTrainingMethod(String trainingMethod) 
    {
        this.trainingMethod = trainingMethod;
    }

    public String getTrainingMethod() 
    {
        return trainingMethod;
    }
    public void setProposedFee(BigDecimal proposedFee) 
    {
        this.proposedFee = proposedFee;
    }

    public BigDecimal getProposedFee() 
    {
        return proposedFee;
    }
    public void setTrainingGuarantee(String trainingGuarantee) 
    {
        this.trainingGuarantee = trainingGuarantee;
    }

    public String getTrainingGuarantee() 
    {
        return trainingGuarantee;
    }
    public void setSuccessCases(String successCases) 
    {
        this.successCases = successCases;
    }

    public String getSuccessCases() 
    {
        return successCases;
    }
    public void setApplicationStatus(String applicationStatus) 
    {
        this.applicationStatus = applicationStatus;
    }

    public String getApplicationStatus() 
    {
        return applicationStatus;
    }
    public void setApplicationTime(Date applicationTime) 
    {
        this.applicationTime = applicationTime;
    }

    public Date getApplicationTime() 
    {
        return applicationTime;
    }
    public void setReviewTime(Date reviewTime) 
    {
        this.reviewTime = reviewTime;
    }

    public Date getReviewTime() 
    {
        return reviewTime;
    }
    public void setReviewer(String reviewer) 
    {
        this.reviewer = reviewer;
    }

    public String getReviewer() 
    {
        return reviewer;
    }
    public void setReviewComment(String reviewComment) 
    {
        this.reviewComment = reviewComment;
    }

    public String getReviewComment() 
    {
        return reviewComment;
    }
    public void setContractStatus(String contractStatus) 
    {
        this.contractStatus = contractStatus;
    }

    public String getContractStatus() 
    {
        return contractStatus;
    }
    public void setContractAmount(BigDecimal contractAmount) 
    {
        this.contractAmount = contractAmount;
    }

    public BigDecimal getContractAmount() 
    {
        return contractAmount;
    }
    public void setContractFileUrl(String contractFileUrl) 
    {
        this.contractFileUrl = contractFileUrl;
    }

    public String getContractFileUrl() 
    {
        return contractFileUrl;
    }
    public void setPerformanceScore(BigDecimal performanceScore) 
    {
        this.performanceScore = performanceScore;
    }

    public BigDecimal getPerformanceScore() 
    {
        return performanceScore;
    }
    public void setPerformanceComment(String performanceComment) 
    {
        this.performanceComment = performanceComment;
    }

    public String getPerformanceComment() 
    {
        return performanceComment;
    }

    public String getOrderTitle() {
        return orderTitle;
    }

    public void setOrderTitle(String orderTitle) {
        this.orderTitle = orderTitle;
    }

    public String getInstitutionName() {
        return institutionName;
    }

    public void setInstitutionName(String institutionName) {
        this.institutionName = institutionName;
    }

    public String getContactPerson() {
        return contactPerson;
    }

    public void setContactPerson(String contactPerson) {
        this.contactPerson = contactPerson;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("applicationId", getApplicationId())
            .append("orderId", getOrderId())
            .append("institutionId", getInstitutionId())
            .append("applicationType", getApplicationType())
            .append("trainingPlan", getTrainingPlan())
            .append("trainingOutline", getTrainingOutline())
            .append("teacherInfo", getTeacherInfo())
            .append("trainingMaterials", getTrainingMaterials())
            .append("trainingMethod", getTrainingMethod())
            .append("proposedFee", getProposedFee())
            .append("trainingGuarantee", getTrainingGuarantee())
            .append("successCases", getSuccessCases())
            .append("applicationStatus", getApplicationStatus())
            .append("applicationTime", getApplicationTime())
            .append("reviewTime", getReviewTime())
            .append("reviewer", getReviewer())
            .append("reviewComment", getReviewComment())
            .append("contractStatus", getContractStatus())
            .append("contractAmount", getContractAmount())
            .append("contractFileUrl", getContractFileUrl())
            .append("performanceScore", getPerformanceScore())
            .append("performanceComment", getPerformanceComment())
            .append("delFlag", getDelFlag())
            .append("createId", getCreateId())
            .append("createTime", getCreateTime())
            .append("updateId", getUpdateId())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
