-- ========================================
-- 更新培训订单表以支持机构招募功能
-- 创建时间：2025-07-23
-- 描述：为现有培训订单表添加机构招募相关字段
-- ========================================

-- 检查字段是否已存在，如果不存在则添加
SET @sql = '';

-- 添加招募状态字段
SELECT COUNT(*) INTO @count FROM information_schema.columns 
WHERE table_schema = DATABASE() AND table_name = 'training_order' AND column_name = 'recruitment_status';
IF @count = 0 THEN
    SET @sql = CONCAT(@sql, 'ALTER TABLE `training_order` ADD COLUMN `recruitment_status` char(1) DEFAULT ''0'' COMMENT ''招募状态(0未开启 1招募中 2已结束)'' AFTER `is_featured`;');
END IF;

-- 添加招募截止时间字段
SELECT COUNT(*) INTO @count FROM information_schema.columns 
WHERE table_schema = DATABASE() AND table_name = 'training_order' AND column_name = 'recruitment_deadline';
IF @count = 0 THEN
    SET @sql = CONCAT(@sql, 'ALTER TABLE `training_order` ADD COLUMN `recruitment_deadline` datetime COMMENT ''招募截止时间'' AFTER `recruitment_status`;');
END IF;

-- 添加最大招募机构数字段
SELECT COUNT(*) INTO @count FROM information_schema.columns 
WHERE table_schema = DATABASE() AND table_name = 'training_order' AND column_name = 'max_institutions';
IF @count = 0 THEN
    SET @sql = CONCAT(@sql, 'ALTER TABLE `training_order` ADD COLUMN `max_institutions` int(11) DEFAULT 1 COMMENT ''最大招募机构数'' AFTER `recruitment_deadline`;');
END IF;

-- 添加当前招募机构数字段
SELECT COUNT(*) INTO @count FROM information_schema.columns 
WHERE table_schema = DATABASE() AND table_name = 'training_order' AND column_name = 'current_institutions';
IF @count = 0 THEN
    SET @sql = CONCAT(@sql, 'ALTER TABLE `training_order` ADD COLUMN `current_institutions` int(11) DEFAULT 0 COMMENT ''当前招募机构数'' AFTER `max_institutions`;');
END IF;

-- 添加机构招募要求字段
SELECT COUNT(*) INTO @count FROM information_schema.columns 
WHERE table_schema = DATABASE() AND table_name = 'training_order' AND column_name = 'recruitment_requirements';
IF @count = 0 THEN
    SET @sql = CONCAT(@sql, 'ALTER TABLE `training_order` ADD COLUMN `recruitment_requirements` text COMMENT ''机构招募要求'' AFTER `current_institutions`;');
END IF;

-- 执行SQL语句
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 更新现有培训订单数据，开启机构招募功能
UPDATE `training_order` 
SET 
    `recruitment_status` = '1',
    `recruitment_deadline` = DATE_ADD(`registration_deadline`, INTERVAL -3 DAY),
    `max_institutions` = 3,
    `current_institutions` = 0,
    `recruitment_requirements` = '具备相应培训资质，有丰富的培训经验，师资力量雄厚'
WHERE `order_status` = '1' AND `recruitment_status` IS NULL;

-- 为已有的培训订单创建索引
ALTER TABLE `training_order` ADD INDEX `idx_recruitment_status` (`recruitment_status`);
ALTER TABLE `training_order` ADD INDEX `idx_recruitment_deadline` (`recruitment_deadline`);

-- 插入一些测试的招募申请数据
INSERT INTO `institution_recruitment_application` 
(`order_id`, `institution_id`, `application_type`, `training_plan`, `training_outline`, `teacher_info`, `training_materials`, `training_method`, `proposed_fee`, `training_guarantee`, `success_cases`, `application_status`, `application_time`, `create_id`, `create_time`) 
VALUES 
(1, 1, 'RECRUITMENT', 
'针对Java高级开发工程师培训的完整培训计划，包含理论学习和实践项目', 
'1. Spring Boot框架深入学习\n2. 微服务架构设计与实现\n3. 分布式系统开发\n4. 项目实战演练', 
'[{"name":"张老师","title":"高级Java架构师","experience":"10年","certification":"Oracle认证专家"},{"name":"李老师","title":"微服务专家","experience":"8年","certification":"Spring认证讲师"}]',
'《Spring Boot实战》、《微服务架构设计模式》等专业教材',
'线下面授+在线辅导',
5500.00,
'提供完整的学习资料，课后答疑，项目指导，就业推荐',
'已成功培训Java工程师500+人，就业率95%以上',
'PENDING',
'2025-07-23 10:00:00',
1,
'2025-07-23 10:00:00'),

(1, 2, 'RECRUITMENT',
'基于职业院校优势的Java培训方案，注重实践能力培养',
'1. Java基础强化\n2. 企业级框架应用\n3. 项目开发实训\n4. 职业素养培训',
'[{"name":"赵老师","title":"软件工程教授","experience":"15年","certification":"高级工程师"},{"name":"钱老师","title":"Java技术专家","experience":"12年","certification":"Oracle认证"}]',
'院校自编教材+企业实战案例',
'线下集中培训',
5200.00,
'学院实训基地支持，企业合作项目，双师型教学',
'与多家IT企业合作，学员就业率98%',
'PENDING',
'2025-07-23 11:30:00',
1,
'2025-07-23 11:30:00');
