package com.sux.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sux.common.annotation.Excel;
import com.sux.common.core.domain.BaseEntity;
import jakarta.validation.constraints.*;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 招聘信息对象 job_posting（核心匹配优化版）
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
@TableName("job_posting")
public class JobPosting extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 招聘ID */
    @TableId(type = IdType.AUTO)
    private Long jobId;

    /** 职位名称 */
    @Excel(name = "职位名称")
    @NotBlank(message = "职位名称不能为空")
    @Size(min = 2, max = 200, message = "职位名称长度必须介于 2 和 200 之间")
    private String jobTitle;

    /** 职位描述 */
    @Excel(name = "职位描述")
    private String jobDescription;

    /** 工作类型（全职/兼职/临时工/小时工） - 核心匹配字段 */
    @Excel(name = "工作类型")
    @NotBlank(message = "工作类型不能为空")
    @Size(min = 0, max = 50, message = "工作类型不能超过50个字符")
    private String jobType;

    /** 工作类别（服务员/保洁/搬运工/销售/厨师助手/快递员/保安等） - 核心匹配字段 */
    @Excel(name = "工作类别")
    @NotBlank(message = "工作类别不能为空")
    @Size(min = 0, max = 100, message = "工作类别不能超过100个字符")
    private String jobCategory;

    /** 工作地点（保留但不作为主要匹配条件） */
    @Excel(name = "工作地点")
    @Size(min = 0, max = 200, message = "工作地点不能超过200个字符")
    private String workLocation;

    /** 薪资类型（hourly/daily/monthly/piece） - 核心匹配字段 */
    @Excel(name = "薪资类型")
    @NotBlank(message = "薪资类型不能为空")
    private String salaryType;

    /** 最低薪资 */
    @Excel(name = "最低薪资")
    @DecimalMin(value = "0.0", message = "最低薪资不能小于0")
    private BigDecimal salaryMin;

    /** 最高薪资 */
    @Excel(name = "最高薪资")
    @DecimalMin(value = "0.0", message = "最高薪资不能小于0")
    private BigDecimal salaryMax;

    /** 学历要求（不限/初中/高中/中专/大专/本科/硕士/博士） - 核心匹配字段 */
    @Excel(name = "学历要求")
    @Size(min = 0, max = 50, message = "学历要求不能超过50个字符")
    private String educationRequired;

    /** 经验要求 */
    @Excel(name = "经验要求")
    @Size(min = 0, max = 100, message = "经验要求不能超过100个字符")
    private String experienceRequired;

    /** 每日工作小时数 */
    @Excel(name = "每日工作小时数")
    @Min(value = 1, message = "每日工作小时数不能小于1")
    @Max(value = 24, message = "每日工作小时数不能大于24")
    private Integer workHoursPerDay;

    /** 每周工作天数 */
    @Excel(name = "每周工作天数")
    @Min(value = 1, message = "每周工作天数不能小于1")
    @Max(value = 7, message = "每周工作天数不能大于7")
    private Integer workDaysPerWeek;

    /** 开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startDate;

    /** 结束日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endDate;

    /** 联系人 */
    @Excel(name = "联系人")
    @Size(min = 0, max = 100, message = "联系人不能超过100个字符")
    private String contactPerson;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "请输入正确的手机号码")
    private String contactPhone;

    /** 公司名称 */
    @Excel(name = "公司名称")
    @Size(min = 0, max = 200, message = "公司名称不能超过200个字符")
    private String companyName;

    /** 紧急程度（urgent/high/normal/low） */
    @Excel(name = "紧急程度")
    private String urgencyLevel;

    /** 招聘人数 */
    @Excel(name = "招聘人数")
    @Min(value = 1, message = "招聘人数不能小于1")
    private Integer positionsAvailable;

    /** 已招聘人数 */
    @Excel(name = "已招聘人数")
    @Min(value = 0, message = "已招聘人数不能小于0")
    private Integer positionsFilled;

    /** 状态（draft/published/paused/closed/completed） */
    @Excel(name = "状态", readConverterExp = "draft=草稿,published=已发布,paused=已暂停,closed=已关闭,completed=已完成")
    private String status;

    /** 浏览次数 */
    @Excel(name = "浏览次数")
    @Min(value = 0, message = "浏览次数不能小于0")
    private Integer viewCount;

    /** 申请次数 */
    @Excel(name = "申请次数")
    @Min(value = 0, message = "申请次数不能小于0")
    private Integer applicationCount;

    /** 发布者用户ID */
    @Excel(name = "发布者用户ID")
    @NotNull(message = "发布者用户ID不能为空")
    private Long publisherUserId;

    /** 是否已验证（0否 1是） */
    @Excel(name = "是否已验证", readConverterExp = "0=否,1=是")
    private Integer isVerified;

    /** 是否推荐（0否 1是） */
    @Excel(name = "是否推荐", readConverterExp = "0=否,1=是")
    private Integer featured;

    /** 删除标志（0代表存在 2代表删除） */
    @TableField("del_flag")
    private String delFlag;

    // 关联查询字段
    /** 发布者用户名 */
    @TableField(exist = false)
    private String publisherUserName;

    /** 发布者昵称 */
    @TableField(exist = false)
    private String publisherNickName;

    /** 发布者手机号 */
    @TableField(exist = false)
    private String publisherPhone;

}
