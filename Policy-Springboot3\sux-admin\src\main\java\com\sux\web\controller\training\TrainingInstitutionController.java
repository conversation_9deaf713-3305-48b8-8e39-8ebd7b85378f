package com.sux.web.controller.training;

import com.sux.common.annotation.Log;
import com.sux.common.core.controller.BaseController;
import com.sux.common.core.domain.AjaxResult;
import com.sux.common.core.page.TableDataInfo;
import com.sux.common.enums.BusinessType;
import com.sux.common.utils.poi.ExcelUtil;
import com.sux.system.domain.TrainingInstitution;
import com.sux.system.service.ITrainingInstitutionService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 培训机构信息Controller
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@RestController
@RequestMapping("/training/institution")
public class TrainingInstitutionController extends BaseController {
    @Autowired
    private ITrainingInstitutionService trainingInstitutionService;

    /**
     * 查询培训机构信息列表
     */
    @PreAuthorize("@ss.hasPermi('training:institution:list')")
    @GetMapping("/list")
    public TableDataInfo list(TrainingInstitution trainingInstitution) {
        startPage();
        List<TrainingInstitution> list = trainingInstitutionService.selectTrainingInstitutionList(trainingInstitution);
        return getDataTable(list);
    }

    /**
     * 导出培训机构信息列表
     */
    @PreAuthorize("@ss.hasPermi('training:institution:export')")
    @Log(title = "培训机构信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TrainingInstitution trainingInstitution) {
        List<TrainingInstitution> list = trainingInstitutionService.selectTrainingInstitutionList(trainingInstitution);
        ExcelUtil<TrainingInstitution> util = new ExcelUtil<TrainingInstitution>(TrainingInstitution.class);
        util.exportExcel(response, list, "培训机构信息数据");
    }

    /**
     * 获取培训机构信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('training:institution:query')")
    @GetMapping(value = "/{institutionId}")
    public AjaxResult getInfo(@PathVariable("institutionId") Long institutionId) {
        return success(trainingInstitutionService.selectTrainingInstitutionByInstitutionId(institutionId));
    }

    /**
     * 新增培训机构信息
     */
    @PreAuthorize("@ss.hasPermi('training:institution:add')")
    @Log(title = "培训机构信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody TrainingInstitution trainingInstitution) {
        if (!trainingInstitutionService.checkInstitutionCodeUnique(trainingInstitution)) {
            return error("新增培训机构'" + trainingInstitution.getInstitutionName() + "'失败，机构编码已存在");
        }
        if (!trainingInstitutionService.checkInstitutionNameUnique(trainingInstitution)) {
            return error("新增培训机构'" + trainingInstitution.getInstitutionName() + "'失败，机构名称已存在");
        }
        return toAjax(trainingInstitutionService.insertTrainingInstitution(trainingInstitution));
    }

    /**
     * 修改培训机构信息
     */
    @PreAuthorize("@ss.hasPermi('training:institution:edit')")
    @Log(title = "培训机构信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody TrainingInstitution trainingInstitution) {
        if (!trainingInstitutionService.checkInstitutionCodeUnique(trainingInstitution)) {
            return error("修改培训机构'" + trainingInstitution.getInstitutionName() + "'失败，机构编码已存在");
        }
        if (!trainingInstitutionService.checkInstitutionNameUnique(trainingInstitution)) {
            return error("修改培训机构'" + trainingInstitution.getInstitutionName() + "'失败，机构名称已存在");
        }
        return toAjax(trainingInstitutionService.updateTrainingInstitution(trainingInstitution));
    }

    /**
     * 删除培训机构信息
     */
    @PreAuthorize("@ss.hasPermi('training:institution:remove')")
    @Log(title = "培训机构信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{institutionIds}")
    public AjaxResult remove(@PathVariable Long[] institutionIds) {
        return toAjax(trainingInstitutionService.deleteTrainingInstitutionByInstitutionIds(institutionIds));
    }

    /**
     * 审核培训机构
     */
    @PreAuthorize("@ss.hasPermi('training:institution:audit')")
    @Log(title = "培训机构审核", businessType = BusinessType.UPDATE)
    @PutMapping("/audit/{institutionId}")
    public AjaxResult audit(@PathVariable Long institutionId, @RequestBody TrainingInstitution trainingInstitution) {
        return toAjax(trainingInstitutionService.auditInstitution(institutionId, 
            trainingInstitution.getStatus(), 
            trainingInstitution.getAuditor(), 
            trainingInstitution.getAuditComment()));
    }

    /**
     * 批量审核培训机构
     */
    @PreAuthorize("@ss.hasPermi('training:institution:audit')")
    @Log(title = "培训机构批量审核", businessType = BusinessType.UPDATE)
    @PutMapping("/batchAudit")
    public AjaxResult batchAudit(@RequestBody TrainingInstitution trainingInstitution) {
        Long[] institutionIds = trainingInstitution.getInstitutionId() != null ? 
            new Long[]{trainingInstitution.getInstitutionId()} : new Long[0];
        return toAjax(trainingInstitutionService.batchAuditInstitutions(institutionIds, 
            trainingInstitution.getStatus(), 
            trainingInstitution.getAuditor(), 
            trainingInstitution.getAuditComment()));
    }

    /**
     * 根据状态查询机构列表
     */
    @PreAuthorize("@ss.hasPermi('training:institution:list')")
    @GetMapping("/status/{status}")
    public AjaxResult getByStatus(@PathVariable String status) {
        List<TrainingInstitution> list = trainingInstitutionService.selectTrainingInstitutionByStatus(status);
        return success(list);
    }

    /**
     * 根据专业领域查询机构列表
     */
    @PreAuthorize("@ss.hasPermi('training:institution:list')")
    @GetMapping("/specialty/{specialty}")
    public AjaxResult getBySpecialty(@PathVariable String specialty) {
        List<TrainingInstitution> list = trainingInstitutionService.selectTrainingInstitutionBySpecialty(specialty);
        return success(list);
    }

    /**
     * 获取机构状态统计
     */
    @PreAuthorize("@ss.hasPermi('training:institution:list')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics() {
        List<TrainingInstitution> statistics = trainingInstitutionService.selectInstitutionStatusStatistics();
        return success(statistics);
    }
}
