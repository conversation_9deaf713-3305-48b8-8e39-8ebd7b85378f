2025/06/26 20:32:24 [error] 7732#35780: *3 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/captchaImage HTTP/1.1", upstream: "http://127.0.0.1:7001/captchaImage", host: "localhost:80", referrer: "http://localhost:81/login?redirect=/index"
2025/06/26 21:49:12 [error] 7732#35780: *527 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/26 21:49:15 [error] 7732#35780: *529 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/26 21:59:17 [error] 7732#35780: *593 upstream timed out (10060: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond) while reading response header from upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/common/upload HTTP/1.1", upstream: "http://127.0.0.1:7001/common/upload", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/26 22:00:13 [error] 7732#35780: *595 upstream timed out (10060: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond) while reading response header from upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/common/upload HTTP/1.1", upstream: "http://127.0.0.1:7001/common/upload", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/26 22:01:32 [error] 7732#35780: *597 upstream timed out (10060: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond) while reading response header from upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/common/upload HTTP/1.1", upstream: "http://127.0.0.1:7001/common/upload", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/26 22:03:33 [notice] 22104#39648: signal process started
2025/06/26 22:04:05 [emerg] 38488#18136: invalid URL prefix in E:\gitData\work\git-open-source\sux-admin\plugs\nginx-1.18.0/conf/domains.d/nginx.conf:32
2025/06/26 22:04:29 [error] 7732#35780: *613 CreateFile() "E:\gitData\work\git-open-source\sux-admin\plugs\nginx-1.18.0/html/sux-file/gitData/work/文件/upload/2025/06/26/阿尔法小队_20250626220411A002.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/upload/2025/06/26/%E9%98%BF%E5%B0%94%E6%B3%95%E5%B0%8F%E9%98%9F_20250626220411A002.png HTTP/1.1", host: "localhost"
2025/06/26 22:04:29 [error] 7732#35780: *613 CreateFile() "E:\gitData\work\git-open-source\sux-admin\plugs\nginx-1.18.0/html/favicon.ico" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/sux-file/gitData/work/%E6%96%87%E4%BB%B6/upload/2025/06/26/%E9%98%BF%E5%B0%94%E6%B3%95%E5%B0%8F%E9%98%9F_20250626220411A002.png"
2025/06/26 22:06:24 [notice] 12392#18588: signal process started
2025/06/26 22:06:27 [error] 7732#35780: *616 CreateFile() "E:\gitData\work\git-open-source\sux-admin\plugs\nginx-1.18.0/html/sux-file/gitData/work/文件/upload/2025/06/26/阿尔法小队_20250626220411A002.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/upload/2025/06/26/%E9%98%BF%E5%B0%94%E6%B3%95%E5%B0%8F%E9%98%9F_20250626220411A002.png HTTP/1.1", host: "localhost"
2025/06/26 22:06:53 [notice] 7328#21608: signal process started
2025/06/26 22:06:53 [error] 7328#21608: OpenEvent("Global\ngx_stop_20124") failed (2: The system cannot find the file specified)
2025/06/26 22:51:59 [error] 18088#32496: *54 CreateFile() "E:\gitData\work\git-open-source\sux-admin\plugs\nginx-1.18.0/html/ruoyi-admin/getInfo" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /ruoyi-admin/getInfo HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/26 22:51:59 [error] 18088#32496: *55 CreateFile() "E:\gitData\work\git-open-source\sux-admin\plugs\nginx-1.18.0/html/ruoyi-admin/logout" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "POST /ruoyi-admin/logout HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 20:21:23 [error] 9796#2740: *1 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/captchaImage HTTP/1.1", upstream: "http://127.0.0.1:7001/captchaImage", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 20:22:05 [error] 9796#2740: *11 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 20:22:05 [error] 9796#2740: *11 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 20:25:36 [error] 9796#2740: *40 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 20:25:36 [error] 9796#2740: *40 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 20:25:45 [error] 9796#2740: *55 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 20:25:45 [error] 9796#2740: *55 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 20:29:06 [error] 9796#2740: *72 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 20:29:06 [error] 9796#2740: *72 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 20:34:05 [error] 9796#2740: *97 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 20:34:05 [error] 9796#2740: *97 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 20:37:13 [error] 9796#2740: *116 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 20:37:13 [error] 9796#2740: *116 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 20:37:51 [error] 9796#2740: *145 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 20:37:51 [error] 9796#2740: *145 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 20:57:09 [error] 9796#2740: *176 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/system/user/ HTTP/1.1", upstream: "http://127.0.0.1:7001/system/user/", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 20:57:11 [error] 9796#2740: *178 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/system/user/ HTTP/1.1", upstream: "http://127.0.0.1:7001/system/user/", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 20:57:13 [error] 9796#2740: *180 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 20:57:15 [error] 9796#2740: *182 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:01:42 [error] 9796#2740: *188 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:01:58 [error] 9796#2740: *213 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:06:14 [error] 9796#2740: *260 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:09:03 [error] 9796#2740: *301 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:09:28 [error] 9796#2740: *316 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:10:19 [error] 9796#2740: *347 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:10:31 [error] 9796#2740: *364 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:11:12 [error] 9796#2740: *395 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:14:36 [error] 9796#2740: *434 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:17:08 [error] 9796#2740: *463 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:19:23 [error] 9796#2740: *580 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:21:39 [error] 9796#2740: *633 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:22:50 [error] 9796#2740: *674 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:24:10 [error] 9796#2740: *729 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:26:08 [error] 9796#2740: *790 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:27:57 [error] 9796#2740: *823 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:28:32 [error] 9796#2740: *838 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:28:41 [error] 9796#2740: *845 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:29:07 [error] 9796#2740: *856 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:30:12 [error] 9796#2740: *873 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:31:56 [error] 9796#2740: *918 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:32:08 [error] 9796#2740: *933 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:32:19 [error] 9796#2740: *944 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:32:35 [error] 9796#2740: *965 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:32:38 [error] 9796#2740: *976 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:33:49 [error] 9796#2740: *991 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:38:26 [error] 9796#2740: *1076 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:41:32 [error] 9796#2740: *1163 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:45:03 [error] 9796#2740: *1236 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:45:16 [error] 9796#2740: *1253 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:47:57 [error] 9796#2740: *1290 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:52:11 [error] 9796#2740: *1333 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:54:44 [error] 9796#2740: *1384 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:55:16 [error] 9796#2740: *1401 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:56:42 [error] 9796#2740: *1426 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:57:45 [error] 9796#2740: *1473 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:58:58 [error] 9796#2740: *1500 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 21:59:16 [error] 9796#2740: *1529 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 22:19:10 [error] 9796#2740: *1598 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 22:41:39 [error] 9796#2740: *1651 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 23:33:11 [error] 9796#2740: *1714 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 23:38:49 [error] 9796#2740: *1755 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 23:46:05 [error] 9796#2740: *1818 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 23:49:10 [error] 9796#2740: *1827 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 23:49:36 [error] 9796#2740: *1852 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 23:51:55 [error] 9796#2740: *1913 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 23:52:12 [error] 9796#2740: *1928 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 23:53:04 [error] 9796#2740: *1943 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 23:53:16 [error] 9796#2740: *1966 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 23:53:47 [error] 9796#2740: *1989 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 23:55:17 [error] 9796#2740: *2016 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 23:56:11 [error] 9796#2740: *2057 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/27 23:59:11 [error] 9796#2740: *2118 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 00:01:26 [error] 9796#2740: *2145 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 00:01:44 [error] 9796#2740: *2170 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 00:02:30 [error] 9796#2740: *2201 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 00:08:06 [error] 9796#2740: *2270 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 10:20:34 [error] 10056#27124: *9 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 11:17:56 [error] 10056#27124: *70 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 11:24:36 [error] 10056#27124: *119 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 11:29:21 [error] 10056#27124: *162 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 11:31:16 [error] 10056#27124: *201 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 11:32:35 [error] 10056#27124: *234 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 11:39:46 [error] 10056#27124: *301 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 11:51:11 [error] 10056#27124: *388 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 11:54:01 [error] 10056#27124: *429 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 12:04:19 [error] 10056#27124: *450 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 12:06:19 [error] 10056#27124: *465 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 12:14:19 [error] 10056#27124: *486 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 12:22:36 [error] 10056#27124: *541 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 12:23:45 [error] 10056#27124: *566 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 12:25:21 [error] 10056#27124: *591 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 12:25:45 [error] 10056#27124: *616 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 12:28:15 [error] 10056#27124: *647 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 12:29:39 [error] 10056#27124: *678 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 12:30:28 [error] 10056#27124: *703 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 12:31:46 [error] 10056#27124: *728 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 12:32:12 [error] 10056#27124: *753 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 12:37:35 [error] 10056#27124: *794 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 12:39:17 [error] 10056#27124: *833 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 12:39:34 [error] 10056#27124: *848 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 12:39:57 [error] 10056#27124: *863 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 12:42:46 [error] 10056#27124: *902 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 12:45:41 [error] 10056#27124: *917 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 12:48:31 [error] 10056#27124: *932 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 12:48:41 [error] 10056#27124: *947 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 12:48:46 [error] 10056#27124: *962 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 12:49:01 [error] 10056#27124: *977 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 12:52:55 [error] 10056#27124: *1014 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 12:53:23 [error] 10056#27124: *1029 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 12:54:19 [error] 10056#27124: *1044 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 12:55:57 [error] 10056#27124: *1063 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 13:02:09 [error] 10056#27124: *1084 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 13:02:28 [error] 10056#27124: *1099 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 13:02:36 [error] 10056#27124: *1114 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 13:02:52 [error] 10056#27124: *1129 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 13:03:06 [error] 10056#27124: *1144 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 13:04:28 [error] 10056#27124: *1159 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 13:05:57 [error] 10056#27124: *1192 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 13:06:06 [error] 10056#27124: *1207 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 13:06:40 [error] 10056#27124: *1222 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 13:08:04 [error] 10056#27124: *1237 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 13:08:48 [error] 10056#27124: *1252 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 13:08:55 [error] 10056#27124: *1267 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 13:09:35 [error] 10056#27124: *1286 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 13:11:18 [error] 10056#27124: *1307 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 13:13:37 [error] 10056#27124: *1346 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 13:13:49 [error] 10056#27124: *1361 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 13:18:03 [error] 10056#27124: *1386 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 13:22:25 [error] 10056#27124: *1425 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 13:23:19 [error] 10056#27124: *1440 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 13:23:59 [error] 10056#27124: *1457 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 13:25:44 [error] 10056#27124: *1474 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 13:29:46 [error] 10056#27124: *1545 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 13:40:26 [error] 10056#27124: *1596 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 13:44:05 [error] 10056#27124: *1651 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 13:44:52 [error] 10056#27124: *1668 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 13:47:52 [error] 10056#27124: *1689 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 13:50:24 [error] 10056#27124: *1710 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 13:54:58 [error] 10056#27124: *1749 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 14:01:46 [error] 10056#27124: *1782 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 14:02:07 [error] 10056#27124: *1797 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 14:02:27 [error] 10056#27124: *1812 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 14:02:32 [error] 10056#27124: *1827 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 14:02:52 [error] 10056#27124: *1848 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 14:03:00 [error] 10056#27124: *1859 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 14:11:43 [error] 10056#27124: *1882 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 14:11:55 [error] 10056#27124: *1897 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 14:14:51 [error] 10056#27124: *1912 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 14:15:09 [error] 10056#27124: *1931 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 14:16:31 [error] 10056#27124: *1944 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 14:25:33 [error] 10056#27124: *1957 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 14:27:51 [error] 10056#27124: *1970 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 14:30:06 [error] 10056#27124: *1983 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 14:36:47 [error] 10056#27124: *1998 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 14:37:02 [error] 10056#27124: *2013 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 14:40:15 [error] 10056#27124: *2028 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 14:41:06 [error] 10056#27124: *2043 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 14:47:49 [error] 10056#27124: *2058 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 14:48:16 [error] 10056#27124: *2073 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 14:49:54 [error] 10056#27124: *2088 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 14:51:35 [error] 10056#27124: *2103 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 14:52:31 [error] 10056#27124: *2130 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 14:56:33 [error] 10056#27124: *2145 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 14:57:02 [error] 10056#27124: *2160 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 15:00:28 [error] 10056#27124: *2175 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 15:04:55 [error] 10056#27124: *2212 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 15:05:59 [error] 10056#27124: *2229 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 15:16:24 [error] 10056#27124: *2258 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 15:16:52 [error] 10056#27124: *2273 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 15:17:01 [error] 10056#27124: *2288 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 15:17:43 [error] 10056#27124: *2303 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 15:19:09 [error] 10056#27124: *2318 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 15:19:29 [error] 10056#27124: *2333 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 15:20:00 [error] 10056#27124: *2348 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 15:20:18 [error] 10056#27124: *2363 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 15:20:45 [error] 10056#27124: *2378 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 15:25:23 [error] 10056#27124: *2397 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 15:27:47 [error] 10056#27124: *2412 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 15:31:31 [error] 10056#27124: *2439 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 15:35:01 [error] 10056#27124: *2454 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 15:38:23 [error] 10056#27124: *2469 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 15:38:43 [error] 10056#27124: *2484 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 15:40:53 [error] 10056#27124: *2533 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 15:41:12 [error] 10056#27124: *2548 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 15:43:41 [error] 10056#27124: *2571 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 15:57:31 [error] 10056#27124: *2632 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 15:57:46 [error] 10056#27124: *2655 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 15:57:56 [error] 10056#27124: *2678 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 15:58:04 [error] 10056#27124: *2701 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 15:58:22 [error] 10056#27124: *2724 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 15:59:01 [error] 10056#27124: *2747 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 16:02:53 [error] 10056#27124: *2846 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 16:05:19 [error] 10056#27124: *2889 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 16:12:03 [error] 10056#27124: *2982 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 16:13:34 [error] 10056#27124: *3007 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 16:19:28 [error] 10056#27124: *3052 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 16:19:34 [error] 10056#27124: *3067 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 16:20:14 [error] 10056#27124: *3114 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 16:29:53 [error] 10056#27124: *3203 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 16:31:47 [error] 10056#27124: *3214 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 16:32:12 [error] 10056#27124: *3237 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 16:32:58 [error] 10056#27124: *3248 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 16:33:30 [error] 10056#27124: *3257 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 16:34:52 [error] 10056#27124: *3290 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 16:37:16 [error] 10056#27124: *3309 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 16:37:18 [error] 10056#27124: *3314 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 16:37:24 [error] 10056#27124: *3319 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 16:37:27 [error] 10056#27124: *3324 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 16:37:47 [error] 10056#27124: *3333 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 16:37:52 [error] 10056#27124: *3344 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 16:41:31 [error] 10056#27124: *3419 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 16:46:47 [error] 10056#27124: *3490 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 16:47:07 [error] 10056#27124: *3503 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 16:47:22 [error] 10056#27124: *3516 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 16:48:20 [error] 10056#27124: *3529 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 16:50:58 [error] 10056#27124: *3564 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 16:51:41 [error] 10056#27124: *3579 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 16:54:09 [error] 10056#27124: *3606 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 16:55:14 [error] 10056#27124: *3641 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 16:55:54 [error] 10056#27124: *3662 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 16:59:55 [error] 10056#27124: *3681 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 17:03:18 [error] 10056#27124: *3774 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 17:03:52 [error] 10056#27124: *3795 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 17:04:19 [error] 10056#27124: *3830 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 17:04:35 [error] 10056#27124: *3839 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 17:04:36 [error] 10056#27124: *3848 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 17:05:01 [error] 10056#27124: *3883 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 17:06:02 [error] 10056#27124: *3920 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 17:12:52 [error] 10056#27124: *3955 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 17:14:58 [error] 10056#27124: *4002 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 17:23:16 [error] 10056#27124: *4099 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 21:56:16 [error] 33408#33532: *1 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/captchaImage HTTP/1.1", upstream: "http://127.0.0.1:7001/captchaImage", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 21:57:29 [error] 33408#33532: *11 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 21:59:41 [error] 33408#33532: *54 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 22:00:31 [error] 33408#33532: *69 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 22:02:02 [error] 33408#33532: *116 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 22:02:48 [error] 33408#33532: *159 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 22:03:02 [error] 33408#33532: *204 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 22:05:08 [error] 33408#33532: *255 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 22:14:26 [error] 33408#33532: *298 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 22:26:29 [error] 33408#33532: *309 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 22:26:31 [error] 33408#33532: *311 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 22:27:02 [error] 33408#33532: *313 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 22:27:04 [error] 33408#33532: *315 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 22:27:11 [error] 33408#33532: *317 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 22:27:13 [error] 33408#33532: *319 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 22:27:56 [error] 33408#33532: *321 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 22:27:58 [error] 33408#33532: *323 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 22:28:43 [error] 33408#33532: *325 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 22:28:45 [error] 33408#33532: *327 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 22:46:21 [error] 33408#33532: *329 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 22:46:23 [error] 33408#33532: *331 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 22:46:52 [error] 33408#33532: *337 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/28 22:46:52 [error] 33408#33532: *337 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/29 00:08:59 [error] 33408#33532: *352 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/29 00:08:59 [error] 33408#33532: *352 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/29 00:10:05 [error] 33408#33532: *377 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/29 00:10:05 [error] 33408#33532: *377 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/29 02:23:56 [error] 33408#33532: *412 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/29 02:23:58 [error] 33408#33532: *414 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/29 02:25:08 [error] 33408#33532: *416 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/29 02:25:10 [error] 33408#33532: *418 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/29 02:28:22 [error] 33408#33532: *420 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/29 02:28:24 [error] 33408#33532: *422 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/29 02:32:32 [error] 33408#33532: *424 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/29 02:32:34 [error] 33408#33532: *426 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/29 03:20:38 [error] 33408#33532: *428 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/"
2025/06/29 03:55:23 [error] 33408#33532: *430 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/captchaImage HTTP/1.1", upstream: "http://127.0.0.1:7001/captchaImage", host: "localhost:80", referrer: "http://localhost:81/login?redirect=/index"
2025/06/29 03:56:17 [error] 33408#33532: *440 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/index"
2025/06/29 03:56:17 [error] 33408#33532: *440 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/index"
2025/06/29 04:38:16 [error] 33408#33532: *489 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/system/role"
2025/06/29 04:38:16 [error] 33408#33532: *489 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/system/role"
2025/06/29 19:42:23 [error] 27616#16492: *17 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/index"
2025/06/29 19:42:23 [error] 27616#16492: *17 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/index"
2025/06/30 19:45:28 [error] 34504#40928: *9 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/index"
2025/06/30 19:45:28 [error] 34504#40928: *9 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/index"
2025/07/09 21:55:18 [error] 30092#54928: *1 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/"
2025/07/09 21:55:20 [error] 30092#54928: *3 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/"
2025/07/09 21:59:34 [error] 30092#54928: *13 upstream timed out (10060: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond) while reading response header from upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/login HTTP/1.1", upstream: "http://127.0.0.1:7001/login", host: "localhost:80", referrer: "http://*************:81/login?redirect=/index"
2025/07/09 21:59:44 [error] 30092#54928: *15 upstream timed out (10060: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond) while reading response header from upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/captchaImage HTTP/1.1", upstream: "http://127.0.0.1:7001/captchaImage", host: "localhost:80", referrer: "http://*************:81/login?redirect=/index"
2025/07/09 22:04:28 [error] 30092#54928: *27 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://*************:81/index"
2025/07/09 22:04:28 [error] 30092#54928: *27 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://*************:81/index"
2025/07/09 22:10:22 [error] 30092#54928: *52 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://*************:81/system/user"
2025/07/09 22:10:22 [error] 30092#54928: *52 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://*************:81/system/user"
2025/07/10 21:44:53 [error] 16856#29372: *1 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/"
2025/07/10 21:45:02 [error] 16856#29372: *3 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/"
2025/07/10 21:46:44 [error] 16856#29372: *17 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/index"
2025/07/10 21:46:44 [error] 16856#29372: *17 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/index"
2025/07/10 21:52:21 [error] 16856#29372: *48 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-app/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7002/getInfo", host: "127.0.0.1", referrer: "http://localhost:83/"
2025/07/10 21:52:21 [error] 16856#29372: *50 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-app/system/dict/data/type/st_follow_status HTTP/1.1", upstream: "http://127.0.0.1:7002/system/dict/data/type/st_follow_status", host: "127.0.0.1", referrer: "http://localhost:83/"
2025/07/10 21:52:21 [error] 16856#29372: *52 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-app/system/dict/data/type/t_st_follow_type HTTP/1.1", upstream: "http://127.0.0.1:7002/system/dict/data/type/t_st_follow_type", host: "127.0.0.1", referrer: "http://localhost:83/"
2025/07/10 21:52:21 [error] 16856#29372: *54 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-app/tran/stFollow/list?pageNum=1&pageSize=5 HTTP/1.1", upstream: "http://127.0.0.1:7002/tran/stFollow/list?pageNum=1&pageSize=5", host: "127.0.0.1", referrer: "http://localhost:83/"
2025/07/10 21:52:21 [error] 16856#29372: *56 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-app/system/carousel/list?platformType=ADMINAPP&roleType=CHAN HTTP/1.1", upstream: "http://127.0.0.1:7002/system/carousel/list?platformType=ADMINAPP&roleType=CHAN", host: "127.0.0.1", referrer: "http://localhost:83/"
2025/07/10 21:52:21 [error] 16856#29372: *58 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-app/system/carousel/list?platformType=ADMINAPP&roleType=CHAN HTTP/1.1", upstream: "http://127.0.0.1:7002/system/carousel/list?platformType=ADMINAPP&roleType=CHAN", host: "127.0.0.1", referrer: "http://localhost:83/"
2025/07/10 21:53:02 [error] 16856#29372: *58 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-app/system/dict/data/type/st_follow_status HTTP/1.1", upstream: "http://127.0.0.1:7002/system/dict/data/type/st_follow_status", host: "127.0.0.1", referrer: "http://localhost:83/"
2025/07/10 21:53:02 [error] 16856#29372: *56 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-app/system/dict/data/type/t_st_follow_type HTTP/1.1", upstream: "http://127.0.0.1:7002/system/dict/data/type/t_st_follow_type", host: "127.0.0.1", referrer: "http://localhost:83/"
2025/07/10 21:53:02 [error] 16856#29372: *54 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-app/tran/stFollow/list?pageNum=1&pageSize=5 HTTP/1.1", upstream: "http://127.0.0.1:7002/tran/stFollow/list?pageNum=1&pageSize=5", host: "127.0.0.1", referrer: "http://localhost:83/"
2025/07/10 21:53:02 [error] 16856#29372: *52 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-app/system/carousel/list?platformType=ADMINAPP&roleType=CHAN HTTP/1.1", upstream: "http://127.0.0.1:7002/system/carousel/list?platformType=ADMINAPP&roleType=CHAN", host: "127.0.0.1", referrer: "http://localhost:83/"
2025/07/10 21:53:02 [error] 16856#29372: *50 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-app/system/carousel/list?platformType=ADMINAPP&roleType=CHAN HTTP/1.1", upstream: "http://127.0.0.1:7002/system/carousel/list?platformType=ADMINAPP&roleType=CHAN", host: "127.0.0.1", referrer: "http://localhost:83/"
2025/07/10 21:53:04 [error] 16856#29372: *50 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-app/tran/stFollow/list?pageNum=1&pageSize=5 HTTP/1.1", upstream: "http://127.0.0.1:7002/tran/stFollow/list?pageNum=1&pageSize=5", host: "127.0.0.1", referrer: "http://localhost:83/"
2025/07/10 21:53:23 [error] 16856#29372: *50 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-app/system/dict/data/type/st_follow_status HTTP/1.1", upstream: "http://127.0.0.1:7002/system/dict/data/type/st_follow_status", host: "127.0.0.1", referrer: "http://localhost:83/"
2025/07/10 21:53:23 [error] 16856#29372: *52 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-app/system/dict/data/type/t_st_follow_type HTTP/1.1", upstream: "http://127.0.0.1:7002/system/dict/data/type/t_st_follow_type", host: "127.0.0.1", referrer: "http://localhost:83/"
2025/07/10 21:53:23 [error] 16856#29372: *54 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-app/tran/stFollow/list?pageNum=1&pageSize=5 HTTP/1.1", upstream: "http://127.0.0.1:7002/tran/stFollow/list?pageNum=1&pageSize=5", host: "127.0.0.1", referrer: "http://localhost:83/"
2025/07/10 21:53:23 [error] 16856#29372: *56 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-app/system/carousel/list?platformType=ADMINAPP&roleType=CHAN HTTP/1.1", upstream: "http://127.0.0.1:7002/system/carousel/list?platformType=ADMINAPP&roleType=CHAN", host: "127.0.0.1", referrer: "http://localhost:83/"
2025/07/10 21:53:23 [error] 16856#29372: *58 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-app/system/carousel/list?platformType=ADMINAPP&roleType=CHAN HTTP/1.1", upstream: "http://127.0.0.1:7002/system/carousel/list?platformType=ADMINAPP&roleType=CHAN", host: "127.0.0.1", referrer: "http://localhost:83/"
2025/07/10 21:53:25 [error] 16856#29372: *48 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-app/system/carousel/list?platformType=ADMINAPP&roleType=CHAN HTTP/1.1", upstream: "http://127.0.0.1:7002/system/carousel/list?platformType=ADMINAPP&roleType=CHAN", host: "127.0.0.1", referrer: "http://localhost:83/"
2025/07/10 21:53:25 [error] 16856#29372: *58 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-app/tran/stFollow/list?pageNum=1&pageSize=5 HTTP/1.1", upstream: "http://127.0.0.1:7002/tran/stFollow/list?pageNum=1&pageSize=5", host: "127.0.0.1", referrer: "http://localhost:83/"
2025/07/10 21:53:27 [error] 16856#29372: *48 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-app/tran/stFollow/list?pageNum=1&pageSize=5 HTTP/1.1", upstream: "http://127.0.0.1:7002/tran/stFollow/list?pageNum=1&pageSize=5", host: "127.0.0.1", referrer: "http://localhost:83/"
2025/07/10 21:53:56 [error] 16856#29372: *48 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-app/system/carousel/list?platformType=ADMINAPP&roleType=CHAN HTTP/1.1", upstream: "http://127.0.0.1:7002/system/carousel/list?platformType=ADMINAPP&roleType=CHAN", host: "127.0.0.1", referrer: "http://localhost:83/"
2025/07/10 21:53:58 [error] 16856#29372: *48 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-app/tran/stFollow/list?pageNum=1&pageSize=5 HTTP/1.1", upstream: "http://127.0.0.1:7002/tran/stFollow/list?pageNum=1&pageSize=5", host: "127.0.0.1", referrer: "http://localhost:83/"
2025/07/10 22:42:16 [error] 16856#29372: *150 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/index"
2025/07/10 22:42:16 [error] 16856#29372: *150 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/index"
2025/07/11 21:07:43 [error] 22024#30424: *14 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/index"
2025/07/11 21:07:43 [error] 22024#30424: *14 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/index"
2025/07/11 21:07:53 [error] 22024#30424: *37 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/system/user"
2025/07/11 21:07:53 [error] 22024#30424: *37 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/system/user"
2025/07/11 21:21:47 [error] 22024#30424: *91 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/index"
2025/07/11 21:21:47 [error] 22024#30424: *91 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/index"
2025/07/11 21:25:03 [error] 22024#30424: *103 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/user/profile"
2025/07/11 21:25:03 [error] 22024#30424: *103 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/user/profile"
2025/07/11 21:25:08 [error] 22024#30424: *110 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/user/profile"
2025/07/11 21:25:08 [error] 22024#30424: *110 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/user/profile"
2025/07/11 21:25:11 [error] 22024#30424: *113 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/user/profile"
2025/07/11 21:25:11 [error] 22024#30424: *113 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/06/26/%E3%80%90%E5%93%B2%E9%A3%8E%E5%A3%81%E7%BA%B8%E3%80%91%E4%BA%8C%E6%AC%A1%E5%85%83%E5%9C%BA%E6%99%AF-%E5%8A%A8%E6%BC%AB%E6%8F%92%E7%94%BB_20250626224803A020.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/user/profile"
2025/07/11 21:25:30 [error] 22024#30424: *116 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-admin/gitData/work/文件/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-admin/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/user/profile"
2025/07/11 21:25:30 [error] 22024#30424: *116 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-admin/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/user/profile"
2025/07/11 21:27:34 [error] 22024#30424: *127 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/user/profile"
2025/07/11 21:27:34 [error] 22024#30424: *127 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/user/profile"
2025/07/11 21:28:13 [error] 22024#30424: *130 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 21:28:13 [error] 22024#30424: *130 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 21:28:13 [error] 22024#30424: *131 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/favicon.ico" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png"
2025/07/11 21:28:13 [error] 22024#30424: *131 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png"
2025/07/11 21:29:31 [error] 22024#30424: *133 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 21:29:31 [error] 22024#30424: *133 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 21:29:31 [error] 22024#30424: *134 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/favicon.ico" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png"
2025/07/11 21:29:31 [error] 22024#30424: *134 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png"
2025/07/11 21:29:37 [error] 22024#30424: *135 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 21:29:37 [error] 22024#30424: *135 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 21:29:38 [error] 22024#30424: *136 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/favicon.ico" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png"
2025/07/11 21:29:38 [error] 22024#30424: *136 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png"
2025/07/11 21:29:48 [error] 22024#30424: *137 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 21:29:48 [error] 22024#30424: *137 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 21:29:48 [error] 22024#30424: *138 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/favicon.ico" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/sux-file/gitData/work/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png"
2025/07/11 21:29:48 [error] 22024#30424: *138 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/sux-file/gitData/work/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png"
2025/07/11 21:30:50 [error] 22024#30424: *140 CreateFile() "E:/gitData/work/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png"" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png%22 HTTP/1.1", host: "localhost"
2025/07/11 21:30:50 [error] 22024#30424: *140 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png%22 HTTP/1.1", host: "localhost"
2025/07/11 21:30:50 [error] 22024#30424: *141 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/favicon.ico" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/sux-file/gitData/work/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png%22"
2025/07/11 21:30:50 [error] 22024#30424: *141 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/sux-file/gitData/work/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png%22"
2025/07/11 21:30:55 [error] 22024#30424: *142 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 21:30:55 [error] 22024#30424: *142 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 21:30:55 [error] 22024#30424: *143 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/favicon.ico" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/sux-file/gitData/work/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png"
2025/07/11 21:30:55 [error] 22024#30424: *143 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/sux-file/gitData/work/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png"
2025/07/11 21:30:58 [error] 22024#30424: *144 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1", host: "localhost"
2025/07/11 21:31:01 [error] 22024#30424: *145 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 21:31:01 [error] 22024#30424: *145 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 21:31:02 [error] 22024#30424: *146 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/favicon.ico" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/sux-file/gitData/work/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png"
2025/07/11 21:31:02 [error] 22024#30424: *146 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/sux-file/gitData/work/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png"
2025/07/11 21:31:02 [error] 22024#30424: *147 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1", host: "localhost"
2025/07/11 21:35:02 [error] 1688#23856: *1 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/work/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/work/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 21:35:02 [error] 1688#23856: *1 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/work/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 21:35:02 [error] 1688#23856: *2 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1", host: "localhost"
2025/07/11 21:35:02 [error] 1688#23856: *3 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/favicon.ico" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/sux-file/work/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png"
2025/07/11 21:35:02 [error] 1688#23856: *3 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/sux-file/work/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png"
2025/07/11 21:36:07 [error] 36908#32828: *1 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/work/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/work/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 21:36:07 [error] 36908#32828: *1 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/work/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 21:36:07 [error] 36908#32828: *2 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1", host: "localhost"
2025/07/11 21:36:07 [error] 36908#32828: *3 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/favicon.ico" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/sux-file/work/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png"
2025/07/11 21:36:07 [error] 36908#32828: *3 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/sux-file/work/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png"
2025/07/11 21:36:51 [error] 36908#32828: *4 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 21:36:51 [error] 36908#32828: *4 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 21:36:51 [error] 36908#32828: *5 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1", host: "localhost"
2025/07/11 21:36:51 [error] 36908#32828: *6 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/favicon.ico" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/sux-file/gitData/work/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png"
2025/07/11 21:36:51 [error] 36908#32828: *6 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/sux-file/gitData/work/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png"
2025/07/11 21:37:11 [error] 36908#32828: *7 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 21:37:11 [error] 36908#32828: *7 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 21:37:11 [error] 36908#32828: *8 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1", host: "localhost"
2025/07/11 21:37:12 [error] 36908#32828: *9 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/favicon.ico" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png"
2025/07/11 21:37:12 [error] 36908#32828: *9 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png"
2025/07/11 21:38:59 [error] 36908#32828: *10 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/gitData/work/文件/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 21:38:59 [error] 36908#32828: *10 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 21:38:59 [error] 36908#32828: *11 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1", host: "localhost"
2025/07/11 21:39:00 [error] 36908#32828: *12 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/favicon.ico" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png"
2025/07/11 21:39:00 [error] 36908#32828: *12 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png"
2025/07/11 21:39:03 [error] 36908#32828: *13 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 21:39:03 [error] 36908#32828: *13 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 21:39:03 [error] 36908#32828: *14 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1", host: "localhost"
2025/07/11 21:39:03 [error] 36908#32828: *15 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/favicon.ico" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png"
2025/07/11 21:39:03 [error] 36908#32828: *15 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png"
2025/07/11 21:44:48 [error] 36908#32828: *16 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 21:44:48 [error] 36908#32828: *16 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 21:44:48 [error] 36908#32828: *17 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1", host: "localhost"
2025/07/11 21:44:48 [error] 36908#32828: *18 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/favicon.ico" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png"
2025/07/11 21:44:48 [error] 36908#32828: *18 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png"
2025/07/11 21:47:39 [notice] 28500#37952: signal process started
2025/07/11 21:47:44 [error] 15460#38288: *1 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 21:47:44 [error] 15460#38288: *1 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 21:47:44 [error] 15460#38288: *2 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1", host: "localhost"
2025/07/11 21:47:44 [error] 15460#38288: *3 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/favicon.ico" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png"
2025/07/11 21:47:44 [error] 15460#38288: *3 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png"
2025/07/11 21:47:45 [error] 15460#38288: *4 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 21:47:45 [error] 15460#38288: *4 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 21:47:46 [error] 15460#38288: *5 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1", host: "localhost"
2025/07/11 21:47:46 [error] 15460#38288: *6 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/favicon.ico" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png"
2025/07/11 21:47:46 [error] 15460#38288: *6 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png"
2025/07/11 21:49:13 [error] 15460#38288: *8 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /hht-admin/getInfo HTTP/1.1", host: "localhost:80", referrer: "http://localhost:82/"
2025/07/11 21:49:13 [error] 15460#38288: *9 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "POST /hht-admin/logout HTTP/1.1", host: "localhost:80", referrer: "http://localhost:82/"
2025/07/11 21:49:28 [error] 15460#38288: *7 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 21:49:28 [error] 15460#38288: *7 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 21:49:28 [error] 15460#38288: *10 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/favicon.ico" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png"
2025/07/11 21:49:28 [error] 15460#38288: *10 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png"
2025/07/11 21:49:28 [error] 15460#38288: *11 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1", host: "localhost"
2025/07/11 21:49:47 [error] 15460#38288: *12 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /hht-admin/getInfo HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/07/11 21:49:47 [error] 15460#38288: *13 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "POST /hht-admin/logout HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/"
2025/07/11 22:14:09 [error] 14616#26464: *1 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /hht-admin/getInfo HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/user/profile"
2025/07/11 22:14:09 [error] 14616#26464: *2 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "POST /hht-admin/logout HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/user/profile"
2025/07/11 22:14:32 [error] 14616#26464: *3 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "GET /hht-admin/getInfo HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/user/profile"
2025/07/11 22:14:32 [error] 14616#26464: *4 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "POST /hht-admin/logout HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/user/profile"
2025/07/11 22:14:57 [error] 14616#26464: *19 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/user/profile"
2025/07/11 22:15:16 [error] 14616#26464: *22 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 22:15:49 [error] 14616#26464: *29 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost:80", referrer: "http://localhost:81/index"
2025/07/11 22:15:54 [error] 14616#26464: *22 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 22:16:15 [error] 14616#26464: *22 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 22:16:18 [error] 14616#26464: *22 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 22:16:51 [notice] 25208#17748: signal process started
2025/07/11 22:17:01 [error] 36484#37220: *1 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 22:17:07 [error] 36484#37220: *1 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 22:17:52 [error] 36484#37220: *4 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "POST /hht-app/logout HTTP/1.1", host: "localhost", referrer: "http://localhost:9090/"
2025/07/11 22:17:53 [error] 36484#37220: *5 rewrite or internal redirection cycle while internally redirecting to "/index.html", client: 127.0.0.1, server: localhost, request: "POST /hht-app/logout HTTP/1.1", host: "localhost", referrer: "http://localhost:9090/"
2025/07/11 22:19:17 [notice] 10852#38752: signal process started
2025/07/11 22:19:17 [error] 10852#38752: CreateFile() "E:\gitData\work\git-open-source\sux-admin\plugs\nginx-1.18.0/logs/nginx.pid" failed (2: The system cannot find the file specified)
2025/07/11 22:19:32 [error] 36484#37220: *6 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/gitData/work/文件/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 22:20:21 [error] 39332#18876: *1 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/gitData/work/文件/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 22:20:23 [error] 39332#18876: *1 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/gitData/work/文件/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 22:20:25 [error] 39332#18876: *1 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/gitData/work/文件/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 22:20:31 [error] 39332#18876: *1 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/syx-file/gitData/work/文件/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /syx-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 22:20:33 [error] 39332#18876: *1 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/syx-file/gitData/work/文件/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /syx-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 22:20:55 [error] 39332#18876: *1 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 22:21:07 [error] 39332#18876: *1 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 22:21:10 [error] 39332#18876: *1 CreateFile() "E:/gitData/work/git-open-source/sux-admin/Sux-Vue3/dist/sux-file/gitData/work/文件/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png" failed (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /sux-file/gitData/work/%E6%96%87%E4%BB%B6/avatar/2025/07/11/%E7%99%BE%E7%9B%B8%E5%AE%88%E6%A2%A6(%E5%85%B3%E7%BE%BD)_20250711212529A001.png HTTP/1.1", host: "localhost"
2025/07/11 22:21:17 [notice] 4632#36496: signal process started
2025/07/11 22:24:43 [notice] 33892#30008: signal process started
2025/07/11 22:27:33 [notice] 39728#34456: signal process started
2025/07/11 22:53:14 [error] 38268#16332: *70 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/captchaImage HTTP/1.1", upstream: "http://127.0.0.1:7001/captchaImage", host: "localhost:80", referrer: "http://localhost:81/login?redirect=/index"
2025/07/11 23:21:39 [error] 38268#16332: *176 upstream timed out (10060: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond) while reading response header from upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-app/login HTTP/1.1", upstream: "http://127.0.0.1:7002/login", host: "127.0.0.1", referrer: "http://localhost:83/"
2025/07/11 23:22:06 [error] 38268#16332: *176 upstream timed out (10060: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond) while reading response header from upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-app/login HTTP/1.1", upstream: "http://127.0.0.1:7002/login", host: "127.0.0.1", referrer: "http://localhost:83/"
2025/07/11 23:27:23 [error] 38268#16332: *189 upstream timed out (10060: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond) while reading response header from upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-app/login HTTP/1.1", upstream: "http://127.0.0.1:7002/login", host: "127.0.0.1", referrer: "http://localhost:83/"
2025/07/21 19:23:04 [error] 26032#24288: *1 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/captchaImage HTTP/1.1", upstream: "http://127.0.0.1:7001/captchaImage", host: "localhost:80", referrer: "http://localhost:81/login?redirect=/index"
2025/07/21 20:25:07 [error] 26032#24288: *241 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/system/menu/list HTTP/1.1", upstream: "http://127.0.0.1:7001/system/menu/list", host: "localhost:80", referrer: "http://localhost:81/system/menu"
2025/07/22 23:47:42 [error] 28232#28804: *2264 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:47:42 [error] 28232#28804: *2261 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/22 23:47:44 [error] 28232#28804: *2264 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:47:44 [error] 28232#28804: *2268 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:47:46 [error] 28232#28804: *2264 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:47:46 [error] 28232#28804: *2268 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:47:48 [error] 28232#28804: *2268 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:47:50 [error] 28232#28804: *2268 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:48:14 [error] 28232#28804: *2294 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/system/worker/list?pageNum=1&pageSize=10 HTTP/1.1", upstream: "http://127.0.0.1:7001/system/worker/list?pageNum=1&pageSize=10", host: "localhost:80", referrer: "http://localhost:81/recruitment/worker"
2025/07/22 23:48:14 [error] 28232#28804: *2298 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:48:14 [error] 28232#28804: *2300 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/22 23:48:15 [error] 28232#28804: *2305 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/system/dict/data/type/sys_user_sex HTTP/1.1", upstream: "http://127.0.0.1:7001/system/dict/data/type/sys_user_sex", host: "localhost:80", referrer: "http://localhost:81/system/user"
2025/07/22 23:48:15 [error] 28232#28804: *2303 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/system/dict/data/type/sys_normal_disable HTTP/1.1", upstream: "http://127.0.0.1:7001/system/dict/data/type/sys_normal_disable", host: "localhost:80", referrer: "http://localhost:81/system/user"
2025/07/22 23:48:15 [error] 28232#28804: *2308 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/system/user/list?pageNum=1&pageSize=10 HTTP/1.1", upstream: "http://127.0.0.1:7001/system/user/list?pageNum=1&pageSize=10", host: "localhost:80", referrer: "http://localhost:81/system/user"
2025/07/22 23:48:15 [error] 28232#28804: *2311 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/system/config/configKey/sys.user.initPassword HTTP/1.1", upstream: "http://127.0.0.1:7001/system/config/configKey/sys.user.initPassword", host: "localhost:80", referrer: "http://localhost:81/system/user"
2025/07/22 23:48:15 [error] 28232#28804: *2307 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/system/user/deptTree HTTP/1.1", upstream: "http://127.0.0.1:7001/system/user/deptTree", host: "localhost:80", referrer: "http://localhost:81/system/user"
2025/07/22 23:48:16 [error] 28232#28804: *2302 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:48:16 [error] 28232#28804: *2298 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:48:17 [error] 28232#28804: *2317 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/system/dict/data/type/sys_normal_disable HTTP/1.1", upstream: "http://127.0.0.1:7001/system/dict/data/type/sys_normal_disable", host: "localhost:80", referrer: "http://localhost:81/system/user"
2025/07/22 23:48:17 [error] 28232#28804: *2315 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/system/dict/data/type/sys_user_sex HTTP/1.1", upstream: "http://127.0.0.1:7001/system/dict/data/type/sys_user_sex", host: "localhost:80", referrer: "http://localhost:81/system/user"
2025/07/22 23:48:18 [error] 28232#28804: *2298 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:48:18 [error] 28232#28804: *2302 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:48:19 [error] 28232#28804: *2325 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/system/config/configKey/sys.user.initPassword HTTP/1.1", upstream: "http://127.0.0.1:7001/system/config/configKey/sys.user.initPassword", host: "localhost:80", referrer: "http://localhost:81/system/user"
2025/07/22 23:48:19 [error] 28232#28804: *2323 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/system/user/list?pageNum=1&pageSize=10 HTTP/1.1", upstream: "http://127.0.0.1:7001/system/user/list?pageNum=1&pageSize=10", host: "localhost:80", referrer: "http://localhost:81/system/user"
2025/07/22 23:48:19 [error] 28232#28804: *2321 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/system/user/deptTree HTTP/1.1", upstream: "http://127.0.0.1:7001/system/user/deptTree", host: "localhost:80", referrer: "http://localhost:81/system/user"
2025/07/22 23:48:19 [error] 28232#28804: *2328 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/system/dict/data/type/sys_user_sex HTTP/1.1", upstream: "http://127.0.0.1:7001/system/dict/data/type/sys_user_sex", host: "localhost:80", referrer: "http://localhost:81/system/user"
2025/07/22 23:48:19 [error] 28232#28804: *2327 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/system/dict/data/type/sys_normal_disable HTTP/1.1", upstream: "http://127.0.0.1:7001/system/dict/data/type/sys_normal_disable", host: "localhost:80", referrer: "http://localhost:81/system/user"
2025/07/22 23:48:20 [error] 28232#28804: *2302 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:48:21 [error] 28232#28804: *2332 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/system/dict/data/type/sys_user_sex HTTP/1.1", upstream: "http://127.0.0.1:7001/system/dict/data/type/sys_user_sex", host: "localhost:80", referrer: "http://localhost:81/system/user"
2025/07/22 23:48:21 [error] 28232#28804: *2334 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/system/dict/data/type/sys_normal_disable HTTP/1.1", upstream: "http://127.0.0.1:7001/system/dict/data/type/sys_normal_disable", host: "localhost:80", referrer: "http://localhost:81/system/user"
2025/07/22 23:48:22 [error] 28232#28804: *2302 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:48:23 [error] 28232#28804: *2337 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/system/dict/data/type/sys_user_sex HTTP/1.1", upstream: "http://127.0.0.1:7001/system/dict/data/type/sys_user_sex", host: "localhost:80", referrer: "http://localhost:81/system/user"
2025/07/22 23:48:24 [error] 28232#28804: *2339 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/system/dict/data/type/sys_normal_disable HTTP/1.1", upstream: "http://127.0.0.1:7001/system/dict/data/type/sys_normal_disable", host: "localhost:80", referrer: "http://localhost:81/system/user"
2025/07/22 23:48:25 [error] 28232#28804: *2341 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/system/user/deptTree HTTP/1.1", upstream: "http://127.0.0.1:7001/system/user/deptTree", host: "localhost:80", referrer: "http://localhost:81/system/user"
2025/07/22 23:48:25 [error] 28232#28804: *2345 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/system/config/configKey/sys.user.initPassword HTTP/1.1", upstream: "http://127.0.0.1:7001/system/config/configKey/sys.user.initPassword", host: "localhost:80", referrer: "http://localhost:81/system/user"
2025/07/22 23:48:25 [error] 28232#28804: *2343 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/system/user/list?pageNum=1&pageSize=10 HTTP/1.1", upstream: "http://127.0.0.1:7001/system/user/list?pageNum=1&pageSize=10", host: "localhost:80", referrer: "http://localhost:81/system/user"
2025/07/22 23:48:26 [error] 28232#28804: *2347 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/system/dict/data/type/sys_user_sex HTTP/1.1", upstream: "http://127.0.0.1:7001/system/dict/data/type/sys_user_sex", host: "localhost:80", referrer: "http://localhost:81/system/user"
2025/07/22 23:48:26 [error] 28232#28804: *2349 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/system/dict/data/type/sys_normal_disable HTTP/1.1", upstream: "http://127.0.0.1:7001/system/dict/data/type/sys_normal_disable", host: "localhost:80", referrer: "http://localhost:81/system/user"
2025/07/22 23:48:28 [error] 28232#28804: *2351 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/system/dict/data/type/sys_user_sex HTTP/1.1", upstream: "http://127.0.0.1:7001/system/dict/data/type/sys_user_sex", host: "localhost:80", referrer: "http://localhost:81/system/user"
2025/07/22 23:48:28 [error] 28232#28804: *2353 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/system/dict/data/type/sys_normal_disable HTTP/1.1", upstream: "http://127.0.0.1:7001/system/dict/data/type/sys_normal_disable", host: "localhost:80", referrer: "http://localhost:81/system/user"
2025/07/22 23:48:30 [error] 28232#28804: *2355 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/system/dict/data/type/sys_user_sex HTTP/1.1", upstream: "http://127.0.0.1:7001/system/dict/data/type/sys_user_sex", host: "localhost:80", referrer: "http://localhost:81/system/user"
2025/07/22 23:48:30 [error] 28232#28804: *2357 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/system/dict/data/type/sys_normal_disable HTTP/1.1", upstream: "http://127.0.0.1:7001/system/dict/data/type/sys_normal_disable", host: "localhost:80", referrer: "http://localhost:81/system/user"
2025/07/22 23:48:32 [error] 28232#28804: *2359 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/system/dict/data/type/sys_user_sex HTTP/1.1", upstream: "http://127.0.0.1:7001/system/dict/data/type/sys_user_sex", host: "localhost:80", referrer: "http://localhost:81/system/user"
2025/07/22 23:48:32 [error] 28232#28804: *2361 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/system/dict/data/type/sys_normal_disable HTTP/1.1", upstream: "http://127.0.0.1:7001/system/dict/data/type/sys_normal_disable", host: "localhost:80", referrer: "http://localhost:81/system/user"
2025/07/22 23:48:40 [error] 28232#28804: *2302 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:48:40 [error] 28232#28804: *2298 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/22 23:48:42 [error] 28232#28804: *2300 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:48:42 [error] 28232#28804: *2302 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:48:44 [error] 28232#28804: *2302 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:48:44 [error] 28232#28804: *2300 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:48:44 [error] 28232#28804: *2369 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/system/user"
2025/07/22 23:48:46 [error] 28232#28804: *2300 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:48:46 [error] 28232#28804: *2372 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/system/user"
2025/07/22 23:48:48 [error] 28232#28804: *2300 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:48:58 [error] 28232#28804: *2378 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:48:58 [error] 28232#28804: *2380 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/22 23:49:00 [error] 28232#28804: *2378 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:49:00 [error] 28232#28804: *2382 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:49:06 [error] 28232#28804: *2396 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:49:06 [error] 28232#28804: *2398 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/22 23:49:08 [error] 28232#28804: *2396 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:49:08 [error] 28232#28804: *2399 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:49:10 [error] 28232#28804: *2399 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:49:10 [error] 28232#28804: *2396 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:49:12 [error] 28232#28804: *2399 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:49:14 [error] 28232#28804: *2399 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:50:06 [error] 28232#28804: *2408 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/1", host: "localhost"
2025/07/22 23:50:06 [error] 28232#28804: *2407 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/1", host: "localhost"
2025/07/22 23:50:07 [error] 28232#28804: *2411 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/1", host: "localhost"
2025/07/22 23:50:08 [error] 28232#28804: *2413 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/1", host: "localhost"
2025/07/22 23:50:32 [error] 28232#28804: *2415 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:50:32 [error] 28232#28804: *2417 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/22 23:50:34 [error] 28232#28804: *2419 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:50:34 [error] 28232#28804: *2417 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:50:36 [error] 28232#28804: *2417 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:50:36 [error] 28232#28804: *2419 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:50:39 [error] 28232#28804: *2417 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:50:39 [error] 28232#28804: *2415 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/22 23:50:41 [error] 28232#28804: *2417 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:50:41 [error] 28232#28804: *2415 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:50:43 [error] 28232#28804: *2415 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:50:43 [error] 28232#28804: *2417 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:50:45 [error] 28232#28804: *2417 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:50:47 [error] 28232#28804: *2417 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:50:53 [error] 28232#28804: *2417 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:50:55 [error] 28232#28804: *2430 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:50:56 [error] 28232#28804: *2440 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/22 23:50:57 [error] 28232#28804: *2430 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:50:59 [error] 28232#28804: *2440 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:50:59 [error] 28232#28804: *2430 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:51:02 [error] 28232#28804: *2430 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:51:03 [error] 28232#28804: *2447 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/22 23:51:05 [error] 28232#28804: *2450 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:51:07 [error] 28232#28804: *2447 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:51:07 [error] 28232#28804: *2450 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:51:10 [error] 28232#28804: *2451 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:51:12 [error] 28232#28804: *2458 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/22 23:51:12 [error] 28232#28804: *2451 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:51:13 [error] 28232#28804: *2461 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:51:15 [error] 28232#28804: *2461 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:51:15 [error] 28232#28804: *2451 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:51:17 [error] 28232#28804: *2451 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:51:19 [error] 28232#28804: *2451 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:52:04 [error] 28232#28804: *2461 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:52:04 [error] 28232#28804: *2451 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/22 23:52:06 [error] 28232#28804: *2458 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:52:06 [error] 28232#28804: *2451 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:52:08 [error] 28232#28804: *2451 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:52:08 [error] 28232#28804: *2458 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:52:10 [error] 28232#28804: *2458 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:52:12 [error] 28232#28804: *2458 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:52:25 [error] 28232#28804: *2476 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/22 23:52:25 [error] 28232#28804: *2478 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:52:25 [error] 28232#28804: *2480 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:52:25 [error] 28232#28804: *2483 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:52:25 [error] 28232#28804: *2482 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:52:52 [error] 28232#28804: *2482 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/22 23:52:52 [error] 28232#28804: *2483 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:52:52 [error] 28232#28804: *2480 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:52:52 [error] 28232#28804: *2478 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:52:52 [error] 28232#28804: *2476 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:52:54 [error] 28232#28804: *2486 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/22 23:52:54 [error] 28232#28804: *2482 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:52:54 [error] 28232#28804: *2483 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:52:54 [error] 28232#28804: *2480 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/22 23:52:54 [error] 28232#28804: *2478 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/23 00:11:39 [error] 28232#28804: *3344 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/23 00:11:39 [error] 28232#28804: *3346 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:11:41 [error] 28232#28804: *3344 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/23 00:11:41 [error] 28232#28804: *3349 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/9 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/9", host: "localhost"
2025/07/23 00:27:18 [error] 28232#28804: *3518 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:27:19 [error] 28232#28804: *3707 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:27:20 [error] 28232#28804: *3518 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:27:22 [error] 28232#28804: *3518 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:40:27 [error] 28232#28804: *3983 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/job/posting/list?pageNum=1&pageSize=10 HTTP/1.1", upstream: "http://127.0.0.1:7001/job/posting/list?pageNum=1&pageSize=10", host: "localhost:80", referrer: "http://localhost:81/recruitment/info"
2025/07/23 00:50:30 [error] 28232#28804: *4182 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:50:30 [error] 28232#28804: *4184 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:50:32 [error] 28232#28804: *4184 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:50:32 [error] 28232#28804: *4182 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:50:34 [error] 28232#28804: *4184 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:50:34 [error] 28232#28804: *4182 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:50:36 [error] 28232#28804: *4184 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:51:07 [error] 28232#28804: *4184 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:51:07 [error] 28232#28804: *4182 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:51:09 [error] 28232#28804: *4182 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:51:11 [error] 28232#28804: *4184 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:51:11 [error] 28232#28804: *4182 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:51:13 [error] 28232#28804: *4182 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:51:16 [error] 28232#28804: *4182 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:51:16 [error] 28232#28804: *4184 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:51:18 [error] 28232#28804: *4182 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:51:27 [error] 28232#28804: *4216 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:51:27 [error] 28232#28804: *4218 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:51:29 [error] 28232#28804: *4216 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:51:29 [error] 28232#28804: *4218 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:51:31 [error] 28232#28804: *4216 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:51:31 [error] 28232#28804: *4218 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:51:33 [error] 28232#28804: *4216 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:51:35 [error] 28232#28804: *4216 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:51:41 [error] 28232#28804: *4216 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:51:41 [error] 28232#28804: *4218 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:51:43 [error] 28232#28804: *4216 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:51:43 [error] 28232#28804: *4218 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:51:45 [error] 28232#28804: *4216 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:51:45 [error] 28232#28804: *4218 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:51:49 [error] 28232#28804: *4218 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:51:49 [error] 28232#28804: *4230 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:51:51 [error] 28232#28804: *4218 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:51:53 [error] 28232#28804: *4230 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:51:53 [error] 28232#28804: *4218 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:51:55 [error] 28232#28804: *4230 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:51:57 [error] 28232#28804: *4230 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:52:00 [error] 28232#28804: *4242 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/1", host: "localhost"
2025/07/23 00:52:00 [error] 28232#28804: *4243 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/1", host: "localhost"
2025/07/23 00:52:06 [error] 28232#28804: *4218 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:52:06 [error] 28232#28804: *4230 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:52:09 [error] 28232#28804: *4252 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:52:09 [error] 28232#28804: *4253 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:52:12 [error] 28232#28804: *4260 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:52:12 [error] 28232#28804: *4262 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:52:14 [error] 28232#28804: *4260 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:52:14 [error] 28232#28804: *4264 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:52:16 [error] 28232#28804: *4264 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:52:18 [error] 28232#28804: *4262 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:52:20 [error] 28232#28804: *4271 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:52:20 [error] 28232#28804: *4270 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:52:22 [error] 28232#28804: *4270 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:52:24 [error] 28232#28804: *4270 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:52:36 [error] 28232#28804: *4270 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:52:36 [error] 28232#28804: *4271 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:52:38 [error] 28232#28804: *4270 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:52:38 [error] 28232#28804: *4271 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:52:40 [error] 28232#28804: *4270 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:52:40 [error] 28232#28804: *4271 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:52:42 [error] 28232#28804: *4270 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:52:44 [error] 28232#28804: *4270 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:53:11 [error] 28232#28804: *4270 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:53:11 [error] 28232#28804: *4271 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:53:13 [error] 28232#28804: *4280 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:53:13 [error] 28232#28804: *4270 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:53:17 [error] 28232#28804: *4271 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:53:17 [error] 28232#28804: *4281 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:53:21 [error] 28232#28804: *4296 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:53:21 [error] 28232#28804: *4298 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:53:23 [error] 28232#28804: *4296 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:53:23 [error] 28232#28804: *4298 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:53:25 [error] 28232#28804: *4296 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:53:25 [error] 28232#28804: *4298 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:53:27 [error] 28232#28804: *4296 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:53:30 [error] 28232#28804: *4298 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:53:30 [error] 28232#28804: *4300 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:53:32 [error] 28232#28804: *4298 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:53:32 [error] 28232#28804: *4300 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:53:34 [error] 28232#28804: *4300 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:53:34 [error] 28232#28804: *4298 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:53:36 [error] 28232#28804: *4298 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:53:40 [error] 28232#28804: *4311 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:53:42 [error] 28232#28804: *4311 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:53:43 [error] 28232#28804: *4321 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:53:46 [error] 28232#28804: *4321 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:53:46 [error] 28232#28804: *4325 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:53:50 [error] 28232#28804: *4331 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:53:53 [error] 28232#28804: *4331 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:53:54 [error] 28232#28804: *4336 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:53:54 [error] 28232#28804: *4338 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:53:56 [error] 28232#28804: *4336 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:53:56 [error] 28232#28804: *4331 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:53:58 [error] 28232#28804: *4336 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:53:58 [error] 28232#28804: *4331 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:54:00 [error] 28232#28804: *4331 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:54:02 [error] 28232#28804: *4331 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:54:08 [error] 28232#28804: *4336 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:54:08 [error] 28232#28804: *4331 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:54:10 [error] 28232#28804: *4338 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:54:10 [error] 28232#28804: *4336 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:54:14 [error] 28232#28804: *4331 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:54:14 [error] 28232#28804: *4353 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:54:16 [error] 28232#28804: *4353 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:54:20 [error] 28232#28804: *4360 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:54:21 [error] 28232#28804: *4364 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:54:21 [error] 28232#28804: *4366 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:54:23 [error] 28232#28804: *4364 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:54:23 [error] 28232#28804: *4366 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:54:25 [error] 28232#28804: *4364 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:54:25 [error] 28232#28804: *4366 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:54:27 [error] 28232#28804: *4364 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:54:29 [error] 28232#28804: *4364 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:54:34 [error] 28232#28804: *4364 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:54:34 [error] 28232#28804: *4366 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:54:36 [error] 28232#28804: *4364 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:54:36 [error] 28232#28804: *4366 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:54:38 [error] 28232#28804: *4366 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:54:40 [error] 28232#28804: *4366 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:54:46 [error] 28232#28804: *4366 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:54:46 [error] 28232#28804: *4364 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:54:48 [error] 28232#28804: *4366 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:54:48 [error] 28232#28804: *4360 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:54:50 [error] 28232#28804: *4360 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:54:50 [error] 28232#28804: *4366 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:54:52 [error] 28232#28804: *4366 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:55:19 [error] 28232#28804: *4366 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:55:19 [error] 28232#28804: *4360 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:55:21 [error] 28232#28804: *4360 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:55:21 [error] 28232#28804: *4366 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:55:23 [error] 28232#28804: *4366 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:55:23 [error] 28232#28804: *4360 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:55:25 [error] 28232#28804: *4366 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:55:27 [error] 28232#28804: *4366 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:55:55 [error] 28232#28804: *4366 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:55:55 [error] 28232#28804: *4360 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:55:57 [error] 28232#28804: *4366 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:55:57 [error] 28232#28804: *4360 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:55:59 [error] 28232#28804: *4360 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:55:59 [error] 28232#28804: *4366 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:56:01 [error] 28232#28804: *4366 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:56:03 [error] 28232#28804: *4366 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:56:49 [error] 28232#28804: *4360 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:56:50 [error] 28232#28804: *4400 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:56:50 [error] 28232#28804: *4409 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:56:52 [error] 28232#28804: *4360 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:56:52 [error] 28232#28804: *4400 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:56:54 [error] 28232#28804: *4400 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:56:54 [error] 28232#28804: *4360 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:56:56 [error] 28232#28804: *4360 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:57:00 [error] 28232#28804: *4400 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:57:00 [error] 28232#28804: *4409 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:57:02 [error] 28232#28804: *4400 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:57:02 [error] 28232#28804: *4409 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:57:04 [error] 28232#28804: *4400 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:57:04 [error] 28232#28804: *4409 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:57:08 [error] 28232#28804: *4400 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:57:08 [error] 28232#28804: *4421 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:57:10 [error] 28232#28804: *4400 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:57:10 [error] 28232#28804: *4421 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:57:12 [error] 28232#28804: *4400 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:57:12 [error] 28232#28804: *4421 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:57:14 [error] 28232#28804: *4400 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:57:16 [error] 28232#28804: *4400 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:57:30 [error] 28232#28804: *4400 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:57:30 [error] 28232#28804: *4421 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:57:33 [error] 28232#28804: *4421 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:57:33 [error] 28232#28804: *4400 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:57:35 [error] 28232#28804: *4400 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:57:35 [error] 28232#28804: *4421 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:57:37 [error] 28232#28804: *4400 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:57:39 [error] 28232#28804: *4400 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:57:45 [error] 28232#28804: *4400 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:57:45 [error] 28232#28804: *4421 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:57:47 [error] 28232#28804: *4421 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:57:49 [error] 28232#28804: *4400 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:57:49 [error] 28232#28804: *4421 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:57:53 [error] 28232#28804: *4421 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:57:56 [error] 28232#28804: *4421 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:57:56 [error] 28232#28804: *4451 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:57:59 [error] 28232#28804: *4451 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:57:59 [error] 28232#28804: *4455 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:58:01 [error] 28232#28804: *4455 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:58:03 [error] 28232#28804: *4461 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:58:06 [error] 28232#28804: *4461 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:58:08 [error] 28232#28804: *4461 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:58:08 [error] 28232#28804: *4468 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:58:10 [error] 28232#28804: *4461 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:58:12 [error] 28232#28804: *4469 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:58:14 [error] 28232#28804: *4475 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:58:14 [error] 28232#28804: *4477 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:58:16 [error] 28232#28804: *4469 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:58:16 [error] 28232#28804: *4475 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:58:18 [error] 28232#28804: *4475 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:58:18 [error] 28232#28804: *4469 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:58:20 [error] 28232#28804: *4469 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:58:22 [error] 28232#28804: *4469 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:58:25 [error] 28232#28804: *4475 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:58:27 [error] 28232#28804: *4477 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:58:27 [error] 28232#28804: *4479 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:58:29 [error] 28232#28804: *4477 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:58:29 [error] 28232#28804: *4475 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:58:31 [error] 28232#28804: *4475 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:58:31 [error] 28232#28804: *4477 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:58:33 [error] 28232#28804: *4475 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:58:35 [error] 28232#28804: *4475 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:58:41 [error] 28232#28804: *4475 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:58:41 [error] 28232#28804: *4477 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:58:43 [error] 28232#28804: *4475 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:58:43 [error] 28232#28804: *4479 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:58:45 [error] 28232#28804: *4475 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:58:45 [error] 28232#28804: *4479 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:58:47 [error] 28232#28804: *4479 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:58:49 [error] 28232#28804: *4479 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:58:51 [error] 28232#28804: *4479 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:58:51 [error] 28232#28804: *4475 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:58:53 [error] 28232#28804: *4477 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:58:53 [error] 28232#28804: *4479 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:58:57 [error] 28232#28804: *4511 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:58:58 [error] 28232#28804: *4515 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:59:01 [error] 28232#28804: *4519 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:59:02 [error] 28232#28804: *4525 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:59:03 [error] 28232#28804: *4527 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:59:03 [error] 28232#28804: *4519 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:59:05 [error] 28232#28804: *4527 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:59:05 [error] 28232#28804: *4525 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:59:08 [error] 28232#28804: *4527 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:59:08 [error] 28232#28804: *4530 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:59:10 [error] 28232#28804: *4527 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:59:10 [error] 28232#28804: *4531 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:59:12 [error] 28232#28804: *4531 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:59:12 [error] 28232#28804: *4527 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:59:14 [error] 28232#28804: *4531 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:59:16 [error] 28232#28804: *4527 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:59:16 [error] 28232#28804: *4530 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:59:19 [error] 28232#28804: *4548 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:59:19 [error] 28232#28804: *4549 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:59:21 [error] 28232#28804: *4548 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:59:21 [error] 28232#28804: *4549 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:59:23 [error] 28232#28804: *4549 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:59:25 [error] 28232#28804: *4548 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:59:27 [error] 28232#28804: *4548 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:59:40 [error] 28232#28804: *4549 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:59:42 [error] 28232#28804: *4555 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:59:42 [error] 28232#28804: *4563 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:59:44 [error] 28232#28804: *4555 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:59:44 [error] 28232#28804: *4549 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:59:46 [error] 28232#28804: *4549 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:59:46 [error] 28232#28804: *4555 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:59:48 [error] 28232#28804: *4549 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:59:50 [error] 28232#28804: *4549 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:59:54 [error] 28232#28804: *4549 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:59:54 [error] 28232#28804: *4555 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 00:59:56 [error] 28232#28804: *4563 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:59:56 [error] 28232#28804: *4549 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:59:58 [error] 28232#28804: *4549 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 00:59:58 [error] 28232#28804: *4563 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:00:00 [error] 28232#28804: *4563 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:00:02 [error] 28232#28804: *4563 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:00:07 [error] 28232#28804: *4563 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:00:07 [error] 28232#28804: *4549 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 01:00:09 [error] 28232#28804: *4555 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:00:09 [error] 28232#28804: *4563 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:00:11 [error] 28232#28804: *4563 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:00:11 [error] 28232#28804: *4555 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:00:13 [error] 28232#28804: *4555 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:00:15 [error] 28232#28804: *4555 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:00:24 [error] 28232#28804: *4555 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:00:26 [error] 28232#28804: *4563 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:00:28 [error] 28232#28804: *4563 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:00:32 [error] 28232#28804: *4563 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:00:36 [error] 28232#28804: *4563 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:00:39 [error] 28232#28804: *4563 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:00:41 [error] 28232#28804: *4563 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:00:47 [error] 28232#28804: *4601 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:00:49 [error] 28232#28804: *4601 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:00:53 [error] 28232#28804: *4607 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:00:55 [error] 28232#28804: *4609 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:00:57 [error] 28232#28804: *4609 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:01:10 [error] 28232#28804: *4616 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:01:12 [error] 28232#28804: *4616 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:01:15 [error] 28232#28804: *4616 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:01:17 [error] 28232#28804: *4616 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:01:19 [error] 28232#28804: *4619 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:01:21 [error] 28232#28804: *4619 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:01:25 [error] 28232#28804: *4616 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:01:27 [error] 28232#28804: *4616 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:01:29 [error] 28232#28804: *4616 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:01:36 [error] 28232#28804: *4616 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:01:39 [error] 28232#28804: *4616 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:01:42 [error] 28232#28804: *4616 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:01:45 [error] 28232#28804: *4633 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:01:47 [error] 28232#28804: *4637 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:01:50 [error] 28232#28804: *4640 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:01:52 [error] 28232#28804: *4642 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:01:54 [error] 28232#28804: *4642 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:02:38 [error] 28232#28804: *4642 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:02:40 [error] 28232#28804: *4640 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:02:42 [error] 28232#28804: *4640 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/8 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/8", host: "localhost"
2025/07/23 01:18:36 [alert] 42460#26760: worker process 27924 exited with code C0000142
2025/07/23 01:18:36 [alert] 42460#26760: could not respawn worker
2025/07/23 19:22:33 [error] 25512#11772: *594 CreateFile() "E:\gitData\work\bussine\receiving-orders_ll\web-site\policy\plugs\nginx-1.18.0/html/favicon.ico" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/"
2025/07/23 20:04:11 [error] 25512#11772: *959 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/job/postings?pageSize=10&pageNum=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/job/postings?pageSize=10&pageNum=1", host: "localhost"
2025/07/23 20:05:30 [error] 25512#11772: *962 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/job/postings?pageSize=10&pageNum=1&jobType=%E5%85%BC%E8%81%8C HTTP/1.1", upstream: "http://127.0.0.1:7001/public/job/postings?pageSize=10&pageNum=1&jobType=%E5%85%BC%E8%81%8C", host: "localhost"
2025/07/23 20:43:57 [error] 25512#11772: *1096 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/system/dict/data/type/sys_normal_disable HTTP/1.1", upstream: "http://127.0.0.1:7001/system/dict/data/type/sys_normal_disable", host: "localhost:80", referrer: "http://localhost:81/"
2025/07/23 20:43:57 [error] 25512#11772: *1098 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/policy/info/list?pageNum=1&pageSize=10 HTTP/1.1", upstream: "http://127.0.0.1:7001/policy/info/list?pageNum=1&pageSize=10", host: "localhost:80", referrer: "http://localhost:81/"
2025/07/23 20:47:10 [error] 25512#11772: *1100 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/personApplicon"
2025/07/23 20:47:12 [error] 25512#11772: *1102 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/"
2025/07/23 20:47:12 [error] 25512#11772: *1104 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/personApplicon"
2025/07/23 20:47:14 [error] 25512#11772: *1106 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/"
2025/07/23 20:47:24 [error] 25512#11772: *1108 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/"
2025/07/23 20:47:26 [error] 25512#11772: *1110 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/"
2025/07/23 20:54:08 [error] 25512#11772: *1112 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/personApplicon"
2025/07/23 20:54:09 [error] 25512#11772: *1114 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/"
2025/07/23 20:54:10 [error] 25512#11772: *1116 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/personApplicon"
2025/07/23 20:54:11 [error] 25512#11772: *1118 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/"
2025/07/23 21:01:45 [error] 25512#11772: *1120 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/personApplicon"
2025/07/23 21:01:45 [error] 25512#11772: *1122 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/"
2025/07/23 21:01:47 [error] 25512#11772: *1124 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/personApplicon"
2025/07/23 21:01:47 [error] 25512#11772: *1126 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/"
2025/07/23 21:04:28 [error] 25512#11772: *1128 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/personApplicon"
2025/07/23 21:04:29 [error] 25512#11772: *1130 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/"
2025/07/23 21:04:30 [error] 25512#11772: *1132 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/personApplicon"
2025/07/23 21:04:31 [error] 25512#11772: *1134 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/"
2025/07/23 21:05:23 [error] 25512#11772: *1136 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/personApplicon"
2025/07/23 21:05:23 [error] 25512#11772: *1138 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/"
2025/07/23 21:05:25 [error] 25512#11772: *1140 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/personApplicon"
2025/07/23 21:05:26 [error] 25512#11772: *1142 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/"
2025/07/23 21:06:18 [error] 25512#11772: *1144 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/personApplicon"
2025/07/23 21:06:20 [error] 25512#11772: *1146 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/personApplicon"
2025/07/23 21:06:20 [error] 25512#11772: *1148 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/"
2025/07/23 21:06:23 [error] 25512#11772: *1150 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/"
2025/07/23 21:07:15 [error] 25512#11772: *1152 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/"
2025/07/23 21:07:15 [error] 25512#11772: *1154 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/personApplicon"
2025/07/23 21:07:17 [error] 25512#11772: *1156 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/"
2025/07/23 21:07:17 [error] 25512#11772: *1158 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/personApplicon"
2025/07/23 21:15:46 [error] 25512#11772: *1234 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/personApplicon"
2025/07/23 21:15:47 [error] 25512#11772: *1236 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/"
2025/07/23 21:15:48 [error] 25512#11772: *1238 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/personApplicon"
2025/07/23 21:15:49 [error] 25512#11772: *1240 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/"
2025/07/23 21:16:05 [error] 25512#11772: *1242 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 21:16:14 [error] 25512#11772: *1242 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 21:16:17 [error] 25512#11772: *1242 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "OPTIONS /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 21:20:24 [error] 25512#11772: *1249 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 21:20:25 [error] 25512#11772: *1252 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/"
2025/07/23 21:20:26 [error] 25512#11772: *1249 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 21:20:27 [error] 25512#11772: *1255 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/"
2025/07/23 21:20:33 [error] 25512#11772: *1257 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/"
2025/07/23 21:20:35 [error] 25512#11772: *1259 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/"
2025/07/23 21:20:35 [error] 25512#11772: *1261 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/getInfo HTTP/1.1", upstream: "http://127.0.0.1:7001/getInfo", host: "localhost:80", referrer: "http://localhost:81/"
2025/07/23 21:20:37 [error] 25512#11772: *1263 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "POST /sux-admin/logout HTTP/1.1", upstream: "http://127.0.0.1:7001/logout", host: "localhost:80", referrer: "http://localhost:81/"
2025/07/23 21:21:00 [error] 25512#11772: *1249 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 21:21:12 [error] 25512#11772: *1276 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
2025/07/23 21:21:18 [error] 25512#11772: *1278 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /sux-admin/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1 HTTP/1.1", upstream: "http://127.0.0.1:7001/public/training/order/list?pageSize=5&pageNum=1&orderStatus=1", host: "localhost"
