-- ========================================
-- 培训机构招募相关数据库表
-- 创建时间：2025-07-23
-- 描述：用于培训机构招募功能的数据库表结构
-- ========================================

-- ----------------------------
-- 1. 培训机构信息表
-- ----------------------------
DROP TABLE IF EXISTS `training_institution`;
CREATE TABLE `training_institution` (
  `institution_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '机构ID',
  `institution_name` varchar(200) NOT NULL COMMENT '机构名称',
  `institution_code` varchar(50) UNIQUE COMMENT '机构编码',
  `legal_person` varchar(50) COMMENT '法人代表',
  `contact_person` varchar(50) NOT NULL COMMENT '联系人',
  `contact_phone` varchar(20) NOT NULL COMMENT '联系电话',
  `contact_email` varchar(100) COMMENT '联系邮箱',
  `institution_address` varchar(500) COMMENT '机构地址',
  `business_license` varchar(50) COMMENT '营业执照号',
  `education_license` varchar(50) COMMENT '办学许可证号',
  `established_date` date COMMENT '成立时间',
  `registered_capital` decimal(15,2) COMMENT '注册资本(万元)',
  `business_scope` text COMMENT '经营范围',
  `institution_type` varchar(50) COMMENT '机构类型(企业培训机构/职业院校/高等院校/政府机构)',
  `qualification_level` varchar(20) COMMENT '资质等级(A级/B级/C级)',
  `teacher_count` int(11) DEFAULT 0 COMMENT '师资数量',
  `training_capacity` int(11) DEFAULT 0 COMMENT '年培训能力(人次)',
  `specialties` text COMMENT '专业领域(JSON格式存储)',
  `institution_intro` text COMMENT '机构简介',
  `website_url` varchar(200) COMMENT '官网地址',
  `logo_url` varchar(500) COMMENT '机构LOGO地址',
  `status` char(1) DEFAULT '0' COMMENT '状态(0待审核 1已认证 2已拒绝 3已禁用)',
  `audit_time` datetime COMMENT '审核时间',
  `auditor` varchar(50) COMMENT '审核人',
  `audit_comment` text COMMENT '审核意见',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_id` bigint(20) COMMENT '创建者ID',
  `create_time` datetime COMMENT '创建时间',
  `update_id` bigint(20) COMMENT '更新者ID',
  `update_time` datetime COMMENT '更新时间',
  `remark` varchar(500) COMMENT '备注',
  PRIMARY KEY (`institution_id`),
  UNIQUE KEY `uk_institution_code` (`institution_code`),
  KEY `idx_institution_name` (`institution_name`),
  KEY `idx_contact_phone` (`contact_phone`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='培训机构信息表';

-- ----------------------------
-- 2. 机构招募申请表
-- ----------------------------
DROP TABLE IF EXISTS `institution_recruitment_application`;
CREATE TABLE `institution_recruitment_application` (
  `application_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `order_id` bigint(20) NOT NULL COMMENT '培训订单ID',
  `institution_id` bigint(20) NOT NULL COMMENT '机构ID',
  `application_type` varchar(20) DEFAULT 'RECRUITMENT' COMMENT '申请类型(RECRUITMENT招募申请/COOPERATION合作申请)',
  `training_plan` text COMMENT '培训计划详情',
  `training_outline` text COMMENT '培训大纲',
  `teacher_info` text COMMENT '师资信息(JSON格式)',
  `training_materials` text COMMENT '培训教材说明',
  `training_method` varchar(100) COMMENT '培训方式(线上/线下/混合)',
  `proposed_fee` decimal(10,2) COMMENT '报价(元)',
  `training_guarantee` text COMMENT '培训保障措施',
  `success_cases` text COMMENT '成功案例',
  `application_status` varchar(20) DEFAULT 'PENDING' COMMENT '申请状态(PENDING待审核/APPROVED已通过/REJECTED已拒绝/CANCELLED已取消)',
  `application_time` datetime NOT NULL COMMENT '申请时间',
  `review_time` datetime COMMENT '审核时间',
  `reviewer` varchar(50) COMMENT '审核人',
  `review_comment` text COMMENT '审核意见',
  `contract_status` varchar(20) COMMENT '合同状态(DRAFT草稿/SIGNED已签署/EXECUTED执行中/COMPLETED已完成/TERMINATED已终止)',
  `contract_amount` decimal(10,2) COMMENT '合同金额',
  `contract_file_url` varchar(500) COMMENT '合同文件地址',
  `performance_score` decimal(3,1) COMMENT '履约评分(1-5分)',
  `performance_comment` text COMMENT '履约评价',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_id` bigint(20) COMMENT '创建者ID',
  `create_time` datetime COMMENT '创建时间',
  `update_id` bigint(20) COMMENT '更新者ID',
  `update_time` datetime COMMENT '更新时间',
  `remark` varchar(500) COMMENT '备注',
  PRIMARY KEY (`application_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_institution_id` (`institution_id`),
  KEY `idx_application_status` (`application_status`),
  KEY `idx_application_time` (`application_time`),
  KEY `idx_contract_status` (`contract_status`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='机构招募申请表';

-- ----------------------------
-- 3. 机构资质文件表
-- ----------------------------
DROP TABLE IF EXISTS `institution_qualification_file`;
CREATE TABLE `institution_qualification_file` (
  `file_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '文件ID',
  `institution_id` bigint(20) NOT NULL COMMENT '机构ID',
  `application_id` bigint(20) COMMENT '申请ID(可为空，表示机构基础资质)',
  `file_type` varchar(50) NOT NULL COMMENT '文件类型(BUSINESS_LICENSE营业执照/EDUCATION_LICENSE办学许可证/TEACHER_CERT师资证书/CASE_MATERIAL案例材料/TRAINING_PLAN培训方案/OTHER其他)',
  `file_name` varchar(200) NOT NULL COMMENT '文件名称',
  `file_url` varchar(500) NOT NULL COMMENT '文件地址',
  `file_size` bigint(20) COMMENT '文件大小(字节)',
  `file_format` varchar(20) COMMENT '文件格式(PDF/DOC/DOCX/JPG/PNG等)',
  `upload_time` datetime NOT NULL COMMENT '上传时间',
  `is_verified` char(1) DEFAULT '0' COMMENT '是否已验证(0未验证 1已验证 2验证失败)',
  `verify_time` datetime COMMENT '验证时间',
  `verify_comment` varchar(500) COMMENT '验证意见',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_id` bigint(20) COMMENT '创建者ID',
  `create_time` datetime COMMENT '创建时间',
  `update_id` bigint(20) COMMENT '更新者ID',
  `update_time` datetime COMMENT '更新时间',
  `remark` varchar(500) COMMENT '备注',
  PRIMARY KEY (`file_id`),
  KEY `idx_institution_id` (`institution_id`),
  KEY `idx_application_id` (`application_id`),
  KEY `idx_file_type` (`file_type`),
  KEY `idx_upload_time` (`upload_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='机构资质文件表';

-- ----------------------------
-- 4. 培训订单机构关联表
-- ----------------------------
DROP TABLE IF EXISTS `training_order_institution`;
CREATE TABLE `training_order_institution` (
  `relation_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `order_id` bigint(20) NOT NULL COMMENT '培训订单ID',
  `institution_id` bigint(20) NOT NULL COMMENT '机构ID',
  `application_id` bigint(20) NOT NULL COMMENT '申请ID',
  `relation_type` varchar(20) DEFAULT 'SELECTED' COMMENT '关联类型(SELECTED已选中/BACKUP备选/COOPERATION合作)',
  `start_time` datetime COMMENT '合作开始时间',
  `end_time` datetime COMMENT '合作结束时间',
  `cooperation_status` varchar(20) DEFAULT 'ACTIVE' COMMENT '合作状态(ACTIVE进行中/COMPLETED已完成/SUSPENDED暂停/TERMINATED终止)',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_id` bigint(20) COMMENT '创建者ID',
  `create_time` datetime COMMENT '创建时间',
  `update_id` bigint(20) COMMENT '更新者ID',
  `update_time` datetime COMMENT '更新时间',
  `remark` varchar(500) COMMENT '备注',
  PRIMARY KEY (`relation_id`),
  UNIQUE KEY `uk_order_institution` (`order_id`, `institution_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_institution_id` (`institution_id`),
  KEY `idx_application_id` (`application_id`),
  KEY `idx_relation_type` (`relation_type`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='培训订单机构关联表';

-- ----------------------------
-- 5. 修改培训订单表，添加机构招募相关字段
-- ----------------------------
ALTER TABLE `training_order`
ADD COLUMN `recruitment_status` char(1) DEFAULT '0' COMMENT '招募状态(0未开启 1招募中 2已结束)' AFTER `is_featured`,
ADD COLUMN `recruitment_deadline` datetime COMMENT '招募截止时间' AFTER `recruitment_status`,
ADD COLUMN `max_institutions` int(11) DEFAULT 1 COMMENT '最大招募机构数' AFTER `recruitment_deadline`,
ADD COLUMN `current_institutions` int(11) DEFAULT 0 COMMENT '当前招募机构数' AFTER `max_institutions`,
ADD COLUMN `recruitment_requirements` text COMMENT '机构招募要求' AFTER `current_institutions`;

-- ----------------------------
-- 6. 插入测试数据
-- ----------------------------

-- 插入培训机构测试数据
INSERT INTO `training_institution` VALUES
(1, '青岛智慧教育培训中心', 'INST001', '张三', '李老师', '13800138001', '<EMAIL>', '青岛市市南区香港中路100号', '91370200123456789X', '*********', '2018-03-15', 500.00, 'IT技能培训、职业技能培训', '企业培训机构', 'A级', 25, 2000, '["IT技能","项目管理","数据分析"]', '专业的IT技能培训机构，拥有丰富的企业培训经验', 'http://www.zhihui.com', '/uploads/logo/zhihui.png', '1', '2025-01-15 10:00:00', 'admin', '资质齐全，通过认证', '0', 1, '2025-01-10 09:00:00', 1, '2025-01-15 10:00:00', '优质培训机构'),

(2, '青岛职业技能学院', 'INST002', '王五', '赵老师', '13800138002', '<EMAIL>', '青岛市黄岛区长江路200号', '91370200987654321Y', 'JX2020002', '2015-09-01', 1000.00, '职业技能培训、学历教育', '职业院校', 'A级', 50, 5000, '["职业技能","管理培训","财务管理"]', '青岛市重点职业技能培训院校', 'http://www.qdzyjn.edu.cn', '/uploads/logo/qdzyjn.png', '1', '2025-01-20 14:00:00', 'admin', '院校资质优秀', '0', 1, '2025-01-18 11:00:00', 1, '2025-01-20 14:00:00', '知名职业院校'),

(3, '海尔大学企业培训部', 'INST003', '刘七', '陈老师', '13800138003', '<EMAIL>', '青岛市崂山区海尔路1号', '91370200456789123Z', 'JX2019003', '2010-06-20', 2000.00, '企业管理培训、领导力培训', '企业培训机构', 'A级', 80, 8000, '["企业管理","领导力","人力资源"]', '海尔集团旗下专业企业培训机构', 'http://university.haier.com', '/uploads/logo/haier.png', '1', '2025-01-25 16:00:00', 'admin', '知名企业培训机构', '0', 1, '2025-01-22 13:00:00', 1, '2025-01-25 16:00:00', '大型企业培训机构');
