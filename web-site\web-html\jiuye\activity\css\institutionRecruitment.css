/* 机构招募申请页面样式 */

/* 页面布局 */
.main-content {
    width: 70%;
    float: left;
    padding-right: 20px;
}

.sidebar {
    width: 28%;
    float: right;
}

/* 区块样式 */
.order-info-section,
.application-form-section {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    padding: 20px;
}

.section-header {
    border-bottom: 2px solid #409EFF;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.section-title {
    color: #333;
    font-size: 20px;
    font-weight: bold;
    margin: 0;
}

.section-subtitle {
    color: #666;
    font-size: 14px;
    margin: 5px 0 0 0;
}

/* 订单信息卡片 */
.order-info-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 20px;
}

.order-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
}

.order-details {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.order-detail-item {
    display: flex;
    align-items: center;
}

.detail-label {
    font-weight: bold;
    color: #666;
    min-width: 80px;
    margin-right: 10px;
}

.detail-value {
    color: #333;
    flex: 1;
}

.recruitment-status {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.recruitment-status.active {
    background-color: #67C23A;
    color: white;
}

.recruitment-status.inactive {
    background-color: #909399;
    color: white;
}

/* 表单样式 */
.form-container {
    max-width: 100%;
}

.form-group {
    margin-bottom: 30px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 20px;
    background: #fafafa;
}

.group-title {
    color: #333;
    font-size: 16px;
    font-weight: bold;
    margin: 0 0 20px 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-item {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.form-item.full-width {
    flex: 1 1 100%;
}

.form-item label {
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
    font-size: 14px;
}

.required {
    color: #f56c6c;
}

.form-item input,
.form-item select,
.form-item textarea {
    padding: 10px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-item input:focus,
.form-item select:focus,
.form-item textarea:focus {
    outline: none;
    border-color: #409EFF;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.form-item textarea {
    resize: vertical;
    min-height: 80px;
}

/* 文件上传样式 */
.upload-section {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.upload-item {
    display: flex;
    flex-direction: column;
}

.upload-item label {
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
    font-size: 14px;
}

.upload-area {
    border: 2px dashed #dcdfe6;
    border-radius: 6px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fafafa;
}

.upload-area:hover {
    border-color: #409EFF;
    background: #f0f8ff;
}

.upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.upload-icon {
    font-size: 24px;
    color: #909399;
}

.upload-placeholder p {
    margin: 0;
    color: #333;
    font-size: 14px;
    font-weight: bold;
}

.upload-tip {
    color: #909399;
    font-size: 12px;
}

/* 按钮样式 */
.form-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background-color: #409EFF;
    color: white;
}

.btn-primary:hover {
    background-color: #337ecc;
}

.btn-secondary {
    background-color: #909399;
    color: white;
}

.btn-secondary:hover {
    background-color: #73767a;
}

/* 侧边栏样式 */
.sidebar-widget {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    padding: 20px;
}

.widget-title {
    color: #333;
    font-size: 16px;
    font-weight: bold;
    margin: 0 0 15px 0;
    padding-bottom: 10px;
    border-bottom: 2px solid #409EFF;
}

.notice-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.notice-list li {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
    color: #666;
    font-size: 14px;
    position: relative;
    padding-left: 20px;
}

.notice-list li:before {
    content: "•";
    color: #409EFF;
    font-weight: bold;
    position: absolute;
    left: 0;
}

.notice-list li:last-child {
    border-bottom: none;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.contact-item {
    display: flex;
    align-items: center;
    font-size: 14px;
}

.contact-label {
    font-weight: bold;
    color: #666;
    min-width: 80px;
}

.contact-value {
    color: #333;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-content {
        width: 100%;
        float: none;
        padding-right: 0;
    }
    
    .sidebar {
        width: 100%;
        float: none;
        margin-top: 20px;
    }
    
    .form-row {
        flex-direction: column;
        gap: 10px;
    }
    
    .order-details {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .upload-section {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .form-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 200px;
    }
}
