export const createWorkerProfileTableOption = () => {
    // 工作类型偏好选项（与招聘信息匹配）
    const jobTypePreferenceOptions = [
        { label: "全职", value: "全职" },
        { label: "兼职", value: "兼职" },
        { label: "临时工", value: "临时工" },
        { label: "小时工", value: "小时工" },
        { label: "实习", value: "实习" }
    ];

    // 工作类别选项（与招聘信息匹配）
    const workCategoryOptions = [
        { label: "服务员", value: "服务员" },
        { label: "保洁", value: "保洁" },
        { label: "搬运工", value: "搬运工" },
        { label: "销售", value: "销售" },
        { label: "客服", value: "客服" },
        { label: "配送员", value: "配送员" },
        { label: "厨师", value: "厨师" },
        { label: "司机", value: "司机" },
        { label: "保安", value: "保安" },
        { label: "其他", value: "其他" }
    ];

    // 薪资类型偏好选项（与招聘信息匹配）
    const salaryTypePreferenceOptions = [
        { label: "小时", value: "hourly" },
        { label: "日薪", value: "daily" },
        { label: "月薪", value: "monthly" },
        { label: "计件", value: "piece" }
    ];

    // 学历选项（与招聘信息匹配）
    const educationLevelOptions = [
        { label: "不限", value: "不限" },
        { label: "初中", value: "初中" },
        { label: "高中", value: "高中" },
        { label: "中专", value: "中专" },
        { label: "大专", value: "大专" },
        { label: "本科", value: "本科" }
    ];

    // 性别选项
    const genderOptions = [
        { label: "男", value: "male" },
        { label: "女", value: "female" }
    ];

    // 状态选项
    const statusOptions = [
        { label: "活跃", value: "active" },
        { label: "不活跃", value: "inactive" }
    ];

    return {
        dialogWidth: '900px',
        dialogHeight: '70vh',
        labelWidth: '100px',
        column: [
            // ==================== 核心匹配字段（必填，用于匹配） ====================
            {
                label: "真实姓名",
                prop: "realName",
                search: true,
                searchSpan: 8,
                minWidth: 120,
                rules: [
                    { required: true, message: "真实姓名不能为空", trigger: "blur" },
                    { min: 2, max: 50, message: "真实姓名长度必须介于 2 和 50 之间", trigger: "blur" }
                ],
                span: 12
            },
            {
                label: "工作类型偏好",
                prop: "jobTypesPreferred",
                search: true,
                searchSpan: 6,
                width: 120,
                align: "center",
                type: "select",
                dicData: jobTypePreferenceOptions,
                span: 8,
                slot: true,
                rules: [
                    { required: true, message: "工作类型偏好不能为空", trigger: "change" }
                ]
            },
            {
                label: "工作类别",
                prop: "workCategories",
                search: true,
                searchSpan: 6,
                width: 120,
                align: "center",
                type: "select",
                dicData: workCategoryOptions,
                span: 8,
                slot: true,
                rules: [
                    { required: true, message: "工作类别不能为空", trigger: "change" }
                ]
            },
            {
                label: "薪资类型偏好",
                prop: "salaryTypePreference",
                search: true,
                searchSpan: 6,
                width: 100,
                align: "center",
                type: "select",
                dicData: salaryTypePreferenceOptions,
                span: 8,
                rules: [
                    { required: true, message: "薪资类型偏好不能为空", trigger: "change" }
                ]
            },
            {
                label: "学历水平",
                prop: "educationLevel",
                search: true,
                searchSpan: 6,
                width: 80,
                align: "center",
                type: "select",
                dicData: educationLevelOptions,
                span: 8,
                rules: [
                    { required: true, message: "学历水平不能为空", trigger: "change" }
                ]
            },
            // ==================== 基础信息 ====================
            {
                label: "当前所在地",
                prop: "currentLocation",
                search: true,
                searchSpan: 8,
                minWidth: 120,
                span: 8,
                rules: [
                    { required: true, message: "当前所在地不能为空", trigger: "blur" },
                    { max: 100, message: "当前所在地不能超过100个字符", trigger: "blur" }
                ]
            },
            {
                label: "性别",
                prop: "gender",
                search: true,
                searchSpan: 6,
                width: 60,
                align: "center",
                type: "select",
                dicData: genderOptions,
                span: 8
            },
            {
                label: "年龄",
                prop: "age",
                search: false,
                width: 60,
                align: "center",
                type: "number",
                span: 8,
                min: 16,
                max: 65,
                rules: [
                    { type: 'number', min: 16, max: 65, message: '年龄必须在16-65之间', trigger: 'blur' }
                ]
            },
            {
                label: "手机号",
                prop: "phone",
                search: true,
                searchSpan: 8,
                minWidth: 120,
                span: 8,
                rules: [
                    { required: true, message: "手机号不能为空", trigger: "blur" },
                    { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
                ]
            },
            {
                label: "工作经验年数",
                prop: "workExperienceYears",
                search: false,
                type: "number",
                span: 8,
                min: 0,
                max: 50,
                rules: [
                    { type: 'number', min: 0, max: 50, message: '工作经验年数必须在0-50之间', trigger: 'blur' }
                ]
            },
            {
                label: "期望最低薪资",
                prop: "salaryExpectationMin",
                search: false,
                type: "number",
                span: 8,
                precision: 0,
                min: 0,
                rules: [
                    { type: 'number', min: 0, message: '期望最低薪资不能小于0', trigger: 'blur' }
                ]
            },
            {
                label: "期望最高薪资",
                prop: "salaryExpectationMax",
                search: false,
                type: "number",
                span: 8,
                precision: 0,
                min: 0,
                rules: [
                    { type: 'number', min: 0, message: '期望最高薪资不能小于0', trigger: 'blur' }
                ]
            },
            {
                label: "技能描述",
                prop: "skills",
                search: false,
                type: "textarea",
                span: 24,
                minRows: 3,
                maxRows: 6,
                showWordLimit: true,
                maxlength: 500,
                placeholder: "请描述您的技能和特长"
            },
            {
                label: "自我介绍",
                prop: "selfIntroduction",
                search: false,
                type: "textarea",
                span: 24,
                minRows: 3,
                maxRows: 6,
                showWordLimit: true,
                maxlength: 500,
                placeholder: "请简单介绍一下自己"
            },
            {
                label: "状态",
                prop: "status",
                search: true,
                searchSpan: 6,
                width: 80,
                align: "center",
                type: "select",
                dicData: statusOptions,
                span: 12,
                slot: true,
                addDisplay: false,
                editDisplay: false,
                value: "active"
            }
        ]
    };
};
